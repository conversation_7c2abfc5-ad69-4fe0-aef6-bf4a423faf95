MP1: Java and Build System Basics
1 Introduction
Thebestwaytolearnsoftwareengineeringistopracticesoftwareengineeringintheactualsoftware
developmentprocess. Therefore,inthisandthefollowingMPs,youwillworkwithareal-worldproject
named <PERSON><PERSON><PERSON> to learn basic concepts, methodologies, and techniques in software engineering.
Jsoup (https://jsoup.org) is a popular Java library for working with HTML. It provides a very
convenientAPIforfetchingURLsandextractingandmanipulatingdata,usingthebestHTML5DOM
methods and CSS selectors.
All our MPs will be based on a specific Jsoup version (https://github.com/CS427-FALL-2025/
jsoup), which you can obtain via the following command:
• git clone https://github.com/CS427-FALL-2025/jsoup
• Note: The command above will download <PERSON>so<PERSON> on your local machine in a directory called
“jsoup”
Please make sure that you obtain the above Jsoup version before you start any of the MPs. You
can use the same copy for all your MPs (i.e., you do not need to clone a fresh copy of the above Jsoup
version for each MP). You can use Mac, Linux, or Windows to complete all the MPs (Mac/Linux will
berecommended). Youarealsorecommendedtoinstalltherequiredsoftwarebelowbeforeproceeding
to the rest of MPs:
• Java: 1.8+
– The Java tutorials: https://docs.oracle.com/javase/tutorial/
– Introduction to programming in Java: https://math.hws.edu/javanotes/index.html
– We use Java 1.8 for grading
• Maven: 3.5+
– Maven in 5min: https://maven.apache.org/guides/getting-started/
maven-in-five-minutes.html
– Maven getting started guide: https://maven.apache.org/guides/getting-started/
index.html
– We use Maven 3.8.8 for grading
• Ant: 1.10+ (needed only for MP1, not later MPs)
– Ant manual: https://ant.apache.org/manual/
– Installation:
∗ Mac: brew install ant
∗ Windows & Linux https://ant.apache.org/manual/install.html
– We use Ant 1.10.14 for grading
• Gradle: 8.0+ (needed only for MP1, not later MPs)
– Gradle manual: https://docs.gradle.org/current/userguide/userguide.html
– Installation: https://gradle.org/install/
1
∗ Mac: brew install gradle@8
· Since we are installing a specific version of gradle, brew doesn’t automatically add
it to the global PATH. You should be able to find the downloaded gradle binary at
/opt/homebrew/opt/gradle@8/bin/gradle
∗ Linux: sdk install gradle 8.10
∗ Windows: https://stackoverflow.com/questions/34856932/
how-do-i-install-gradle-on-windows-10
– We use Gradle 8.10 for grading
This Jsoup version comes with a default build file in Maven (i.e., pom.xml); you should be able
to directly run mvn test to execute all its 658 JUnit tests. To run this command, ensure you see the
pom.xml file in your path. Otherwise, you will receive the following error:
[ERROR] The goal you specified requires a project to execute but there is no POM in the
current directory
If successful, it takes a few minutes to build the project (the first time, when the dependencies
are downloaded, and it takes less time later) and execute the test suite. You should see the following
message at the end:
Results :
Tests run: 658 , Failures: 0, Errors: 0, Skipped: 0
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time: 2.269 s
[INFO] Finished at: 2025-08-25T14:23:31-05:00
[INFO] ------------------------------------------------------------------------
Task: You are expected to write two other build files under Ant and Gradle, respec-
tively, to run the same set of tests. Please follow the steps below to finish these tasks:
• If you want to refer to the lecture on “GitHub and Build Systems”, you can check out the PDF
version on coursera.
• You can find the examples discussed in the lecture on different build systems, including Maven,
Ant, and Gradle, here: https://github.com/CS427-FALL-2025/scm-example. You are re-
quired to reuse and modify them for this MP.
• Write two build files, namely “build.xml” and “build.gradle”, to complete the tasks for this MP.
2 Instructions for Ant
• Yourbuildfileshouldincludethefollowingsteps(avoidincludingothersteps,asthismayinterfere
with the auto-grading):
– Variable declarations: All the jar files used in the Maven build file have been downloaded
for you and stored in the ant-libs directory. In the path declaration tag, introduce all the
jar files in the ”ant-libs” directory. Not including these jar files in the path declaration will
failthetestexecution. Userelativepathstoaccessthelibswhenbuildingtheproject. Your
path id in the Ant build file should be set to your NetID.classpath.
– CompileallsourceJavafilesfromsrc/main/javatoant-target/classes. Youcanachieve
this via modifying the build target from “scm-example”. Please make sure that you in-
clude the encoding=”UTF-8” option for javac to avoid encoding issues on some specific OS
settings.
2
– Compile all test Java files from src/test/java to ant-target/test-classes. You can
achieve this via modifying the build-test target from “scm-example”. Please make sure
that you include the encoding="UTF-8" option for javac to avoid encoding issues on some
specific OS settings.
– Copy all test resource files from src/test/resources to ant-target/test-classes. You
can either add this to the existing build-test target or define a new target. If you define
a new target, make sure that the “test” target depends on the new target so that the tests
can access the resource files.
– Definetheactualtestexecutiontarget. Youonlyneedtochangetheclasspathsettingforthe
test target from “scm-example”. Please change the JUnit testing report output directory
(under junitreport tag in the build file) to ant-target/reports, so that the HTML
JUnit testing reports will be generated under the directory ant-target/reports/html/;
otherwise, theautogradermaynotbeabletofindtheHTMLreportforyourtestexecution
and you may lose all your points.
• You can test the correctness of written build files via the following commands: ant clean test
Note that you may receive the “build successful” message in the command line, while your tests failed
during execution. You should look into the test results to ensure the correct build and test execution
(all the tests pass). You will get the full score only if the “success rate” in the HTML report is 100%.
Ant does not print the test execution results by default. To obtain the number of test executions, you
can check the corresponding HTML report: ant-target/reports/html/index.html (automatically
generated once you follow the above instructions and correctly configure the report output directory
under junitreport). Please make sure that you can generate the HTML report under the specified
report path; otherwise you may lose all the points.
Figure 1: Example of an Ant test report where some tests fail because the test resources are not
properly copied
3 Instructions for Gradle
• Your build file should include the following components:
– Plugins: Identifies that the build is for a Java project.
– Repositories: Use https://repo.maven.apache.org/maven2 to resolve all the required
dependencies in the project.
3
Figure 2: Example of a successful Ant build report (note the difference in the success rate)
– Dependencies: You should specify the same dependency versions used in the Maven build
file so that Gradle can automatically fetch them from the Maven repo, or directly use the
downloaded jar files in the ant-libs directory. Failing to do so will result in exceptions
while running the build file.
– Description: ThevaluefordescriptionshouldbeyourNetID(description = ‘Your NetID’)
– Output directory: Insteadofusingthedefaultbuildoutputdirectory,yourbuildfileshould
use gradle-target as your output directory to store all the build outputs (including class
files and reports).
• You can test the correctness of written build files via the following commands: gradle test
Note that you may receive the “build successful” message in the command line, while your tests failed
during execution. You should look into the test results to ensure the correct build and test execution
(all the tests pass). You will get the full score if the “success” in the HTML report is 100%. Like Ant,
Gradle does not print the test execution results by default. To obtain the number of test executions,
you can check the corresponding HTML report: gradle-target/reports/tests/test/index.html
(default report directory for Gradle once you set the output directory to gradle-target). Here is the
example output for Gradle:
Figure 3: A successful build results in 100% success for test execution in the report
4 Deliverables
You are expected to upload a zip file including only the build.xml and build.gradle files you
completed (no directories please). The name of the zip file should be your NetID. In other words,
the hierarchy of your submission zip file should be like this (replace YourNetID with your NetID):
YourNetID.zip
4
|-- build.xml
|-- build.gradle
To create such a zip file, you can run the following command in your terminal:
zip YourNetID.zip build.xml build.gradle
The autograder will copy your zip file into the jsoup directory, unzip it, and run the build files.
Failing to follow this structure may result in losing all the points.
Warning: you may lose all your points if you violate the following rules:
• Please DO NOT use any absolute paths in your solution since your absolute paths will not
match our grading machines. Also, please only use “/” as the file separator in your relative
paths (if needed for this MP) since Java will usually make the correct conversions if you use the
Unix file separator “/” in relative paths (for both Windows and Unix-style systems).
• PleaseDONOTforkanyassignmentrepofromourGitHuborganization,CS427-FALL-2025,
or share your solution online.
• Please make sure that you follow the detailed instructions to use the specified output direc-
tories. The autograder will only look for the specified directories; thus you may lose all points if
you fail to use the same directories (even if you can pass all tests).
5 Grading rubric
Overall score (5pt)
• Antbuildfile(3pt): theautograderwillcheckant-target/reports/html/overview-summary.html
– If you pass all 658 tests, you get 3pt
– Else if you pass 600+ tests, you get 2pt
– Else if you pass some tests, you get 1pt
– Else you get 0pt
• Gradlebuildfile(2pt): theautograderwillcheckgradle-target/reports/tests/test/index.html
– If you pass all 658 tests, you get 2pt
– Else if you pass 600+ tests, you get 1pt
– Else you get 0pt
5
