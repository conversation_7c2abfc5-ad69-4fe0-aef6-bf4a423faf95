<?xml version="1.0" encoding="UTF-8" ?>
<testsuites>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="0" name="DataUtilTest" package="org.jsoup.helper" skipped="0" tests="15" time="0.103" timestamp="2025-09-14T06:46:59">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.helper.DataUtilTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.helper.DataUtilTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher9508070916632117751.properties propsfile=/Users/<USER>/Desktop/jsoup/junit15071656801336306784.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.helper.DataUtilTest" name="firstMetaElementWithCharsetShouldBeUsedForDecoding" time="0.029" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="secondMetaElementWithContentTypeContainsCharsetParameter" time="0.006" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="supportsUTF8BOM" time="0.002" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="generatesMimeBoundaries" time="0.0" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="discardsSpuriousByteOrderMarkWhenNoCharsetSet" time="0.0" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="discardsSpuriousByteOrderMark" time="0.001" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="shouldCorrectCharsetForDuplicateCharsetString" time="0.0" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="shouldNotThrowExceptionOnEmptyCharset" time="0.0" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="testCharset" time="0.002" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="supportsBOMinFiles" time="0.004" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="testQuotedCharset" time="0.001" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="wrongMetaCharsetFallback" time="0.002" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="shouldSelectFirstCharsetOnWeirdMultileCharsetsInMetaTags" time="0.001" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="shouldReturnNullForIllegalCharsetNames" time="0.0" />

      <testcase classname="org.jsoup.helper.DataUtilTest" name="supportsXmlCharsetDeclaration" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="1" name="HttpConnectionTest" package="org.jsoup.helper" skipped="0" tests="23" time="0.072" timestamp="2025-09-14T06:46:59">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.helper.HttpConnectionTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.helper.HttpConnectionTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher14718646934270915654.properties propsfile=/Users/<USER>/Desktop/jsoup/junit3336693168029679784.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsExceptionOnParseWithoutExecute" time="0.009" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="connectWithUrl" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="ignoresEmptySetCookies" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsOnMalformedUrl" time="0.001" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="multipleHeaders" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="cookie" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="timeout" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="method" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="handlesHeaderEncodingOnRequest" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="referrer" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="sameHeadersCombineWithComma" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="inputStream" time="0.001" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsExceptionOnBodyAsBytesWithoutExecute" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="data" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="userAgent" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsExceptionOnBodyWithoutExecute" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="headers" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="noUrlThrowsValidationError" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="requestBody" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="ignoresEmptyCookieNameAndVals" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="encodeUrl" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsOnOddData" time="0.0" />

      <testcase classname="org.jsoup.helper.HttpConnectionTest" name="caseInsensitiveHeaders" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="2" name="W3CDomTest" package="org.jsoup.helper" skipped="0" tests="6" time="0.222" timestamp="2025-09-14T06:47:00">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.helper.W3CDomTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.helper.W3CDomTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher10604912210160615690.properties propsfile=/Users/<USER>/Desktop/jsoup/junit3113514544003551375.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.helper.W3CDomTest" name="simpleConversion" time="0.059" />

      <testcase classname="org.jsoup.helper.W3CDomTest" name="namespacePreservation" time="0.005" />

      <testcase classname="org.jsoup.helper.W3CDomTest" name="convertsGoogleLocation" time="0.068" />

      <testcase classname="org.jsoup.helper.W3CDomTest" name="treatsUndeclaredNamespaceAsLocalName" time="0.003" />

      <testcase classname="org.jsoup.helper.W3CDomTest" name="handlesInvalidAttributeNames" time="0.002" />

      <testcase classname="org.jsoup.helper.W3CDomTest" name="convertsGoogle" time="0.032" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="3" name="ParseTest" package="org.jsoup.integration" skipped="0" tests="12" time="0.177" timestamp="2025-09-14T06:47:00">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.integration.ParseTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.integration.ParseTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher2574252574439779510.properties propsfile=/Users/<USER>/Desktop/jsoup/junit12079870873493372359.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.integration.ParseTest" name="testYahooJp" time="0.049" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testBaiduVariant" time="0.009" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testGoogleSearchIpod" time="0.024" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testBaidu" time="0.003" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testBinaryThrowsException" time="0.002" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testSmhBizArticle" time="0.008" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testYahooArticle" time="0.008" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testBrokenHtml5CharsetWithASingleDoubleQuote" time="0.001" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testNytArticle" time="0.006" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testLowercaseUtf8Charset" time="0.001" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testNewsHomepage" time="0.011" />

      <testcase classname="org.jsoup.integration.ParseTest" name="testHtml5Charset" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="4" name="StringUtilTest" package="org.jsoup.internal" skipped="0" tests="9" time="0.089" timestamp="2025-09-14T06:47:00">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.internal.StringUtilTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.internal.StringUtilTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3051292205468278315.properties propsfile=/Users/<USER>/Desktop/jsoup/junit7011205300139646217.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.internal.StringUtilTest" name="isWhitespace" time="0.001" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="padding" time="0.001" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="join" time="0.0" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="paddingInACan" time="0.0" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="normaliseWhiteSpaceHandlesHighSurrogates" time="0.03" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="isNumeric" time="0.0" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="resolvesRelativeUrls" time="0.002" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="normaliseWhiteSpace" time="0.0" />

      <testcase classname="org.jsoup.internal.StringUtilTest" name="isBlank" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="5" name="AttributeTest" package="org.jsoup.nodes" skipped="0" tests="6" time="0.071" timestamp="2025-09-14T06:47:00">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.AttributeTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.AttributeTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher4534303083044549830.properties propsfile=/Users/<USER>/Desktop/jsoup/junit17562870100875934632.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.AttributeTest" name="settersOnOrphanAttribute" time="0.001" />

      <testcase classname="org.jsoup.nodes.AttributeTest" name="testWithSupplementaryCharacterInAttributeKeyAndValue" time="0.013" />

      <testcase classname="org.jsoup.nodes.AttributeTest" name="html" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributeTest" name="booleanAttributesAreEmptyStringValues" time="0.01" />

      <testcase classname="org.jsoup.nodes.AttributeTest" name="validatesKeysNotEmpty" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributeTest" name="validatesKeysNotEmptyViaSet" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="6" name="AttributesTest" package="org.jsoup.nodes" skipped="0" tests="8" time="0.062" timestamp="2025-09-14T06:47:01">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.AttributesTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.AttributesTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3826471712361717256.properties propsfile=/Users/<USER>/Desktop/jsoup/junit14794947746190536051.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.AttributesTest" name="testIteratorRemovable" time="0.002" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="testIteratorHasNext" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="removeCaseSensitive" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="html" time="0.012" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="testSetKeyConsistency" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="testIteratorEmpty" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="testIterator" time="0.0" />

      <testcase classname="org.jsoup.nodes.AttributesTest" name="testIteratorUpdateable" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="7" name="DocumentTest" package="org.jsoup.nodes" skipped="0" tests="28" time="0.144" timestamp="2025-09-14T06:47:01">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.DocumentTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.DocumentTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher14597638741065161972.properties propsfile=/Users/<USER>/Desktop/jsoup/junit4426301054271208333.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testOutputEncoding" time="0.026" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateDisabledNoChanges" time="0.002" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testHtmlAppendable" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testLocation" time="0.046" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testClonesDeclarations" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testClone" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testShiftJisRoundtrip" time="0.006" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testNormalisesStructure" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testXhtmlReferences" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateXmlNoCharset" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateUtf8" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateEnabledAfterCharsetChange" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateDisabled" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateXmlDisabled" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateCleanup" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="DocumentsWithSameContentAreEqual" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdatedDisabledPerDefault" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateXmlIso8859" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="parseAndHtmlOnDifferentThreads" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateNoCharset" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="DocumentsWithSameContentAreVerifialbe" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="setTextPreservesDocumentStructure" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="htmlParseDefaultsToHtmlOutputSyntax" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateIso8859" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateXmlUtf8" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testHtmlAndXmlSyntax" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testTitles" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTest" name="testMetaCharsetUpdateXmlDisabledNoChanges" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="8" name="DocumentTypeTest" package="org.jsoup.nodes" skipped="0" tests="5" time="0.077" timestamp="2025-09-14T06:47:01">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.DocumentTypeTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.DocumentTypeTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher5635662423486276820.properties propsfile=/Users/<USER>/Desktop/jsoup/junit10355218289863840106.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.DocumentTypeTest" name="outerHtmlGeneration" time="0.018" />

      <testcase classname="org.jsoup.nodes.DocumentTypeTest" name="constructorValidationOkWithBlankName" time="0.001" />

      <testcase classname="org.jsoup.nodes.DocumentTypeTest" name="constructorValidationOkWithBlankPublicAndSystemIds" time="0.0" />

      <testcase classname="org.jsoup.nodes.DocumentTypeTest" name="testRoundTrip" time="0.009" />

      <testcase classname="org.jsoup.nodes.DocumentTypeTest" name="constructorValidationThrowsExceptionOnNulls" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="9" name="ElementTest" package="org.jsoup.nodes" skipped="0" tests="99" time="0.131" timestamp="2025-09-14T06:47:01">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.ElementTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.ElementTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher18307609481204822859.properties propsfile=/Users/<USER>/Desktop/jsoup/junit16314230491741970411.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.ElementTest" name="testFormatOutline" time="0.027" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="parentlessToString" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="insertChildrenArgumentValidation" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testPrependElement" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testOuterHtml" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testHashcodeIsStableWithContentChanges" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="voidTestFilterCallReturnsElement" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="elementByTagName" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testClonesClassnames" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="appendMustCorrectlyMoveChildrenInsideOneParentElement" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="before" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testRemoveBeforeIndex" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testChainedRemoveAttributes" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="booleanAttributeOutput" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testAddNewHtml" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testAddNewText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testPreviousElementSiblings" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetElementById" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testClone" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testHtmlContainsOuter" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testClassDomMethods" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testRoot" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testWrap" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNamespacedElements" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testClassNames" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testElementSiblingIndex" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testIs" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testEmptyElementFormatHtml" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testLoopedRemoveAttributes" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testShadowElementsAreUpdated" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testKeepsPreTextAtDepth" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetElementsWithAttribute" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNoIndentOnScriptAndStyle" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testAppendRowToTable" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetElementsWithClass" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testShallowClone" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testWrapWithRemainder" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testClearAttributes" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testKeepsPreTextInCode" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testHasClassDomMethods" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetParents" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="getElementsByTagName" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testFormatHtml" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetElementsWithAttributeValue" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testWholeText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetElementsWithAttributeDash" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testManipulateTextNodes" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testChildThrowsIndexOutOfBoundsOnMissing" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testPrependRowToTable" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetTextNodes" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="after" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testContainerOutput" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testInnerHtml" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testHasText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testRemoveAttr" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="textHasSpaceBetweenDivAndCenterTags" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetDataNodes" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testCssPath" time="0.002" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testSetHtmlTitle" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testChildrenElements" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="moveByAppend" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNormalisesText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testClassUpdates" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testAddNewElement" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testHashAndEqualsAndValue" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testElementSiblingIndexSameContent" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetChildText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testRelativeUrls" time="0.002" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testSetIndent" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testThrowsOnAddNullText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetSiblingsWithDuplicateContent" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNormalizesInvisiblesInText" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="insertChildrenAsCopy" time="0.002" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testTagNameSet" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="dataset" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testPrependText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNormalizesNbspInText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testRemoveAfterIndex" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testThrowsOnPrependNullText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testGetSiblings" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testSetHtml" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testSetText" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNextElementSiblings" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testAddBooleanAttribute" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="textHasSpaceAfterBlockTags" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="whiteSpaceClassElement" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="classNamesAndAttributeNameIsCaseInsensitive" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testPrependNewHtml" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testRemovingEmptyClassAttributeWhenLastClassRemoved" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testBrHasSpace" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testKeepsPreText" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNotPretty" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testNextElementSiblingAfterClone" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testAppendTo" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="getNamespacedElementsByTag" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="testTraverse" time="0.0" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="insertChildrenAtPosition" time="0.001" />

      <testcase classname="org.jsoup.nodes.ElementTest" name="elementIsNotASiblingOfItself" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="10" name="EntitiesTest" package="org.jsoup.nodes" skipped="0" tests="15" time="0.102" timestamp="2025-09-14T06:47:02">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.EntitiesTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.EntitiesTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3244960826471449371.properties propsfile=/Users/<USER>/Desktop/jsoup/junit6534423663233721855.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="strictUnescape" time="0.013" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="escapeSupplementaryCharacter" time="0.001" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="escapesGtInXmlAttributesButNotInHtml" time="0.01" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="escape" time="0.001" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="unescape" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="caseSensitive" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="noSpuriousDecodes" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="notMissingMultis" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="xhtml" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="getByName" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="notMissingSupplementals" time="0.001" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="quoteReplacements" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="escapedSupplementary" time="0.0" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="letterDigitEntities" time="0.001" />

      <testcase classname="org.jsoup.nodes.EntitiesTest" name="unescapeMultiChars" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="11" name="FormElementTest" package="org.jsoup.nodes" skipped="0" tests="11" time="0.083" timestamp="2025-09-14T06:47:02">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.FormElementTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.FormElementTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher12550362871755987101.properties propsfile=/Users/<USER>/Desktop/jsoup/junit5837350257042089530.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.FormElementTest" name="createsFormData" time="0.027" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="actionWithNoValue" time="0.003" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="removeFormElement" time="0.001" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="formsAddedAfterParseAreFormElements" time="0.001" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="createsSubmitableConnection" time="0.0" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="formDataUsesFirstAttribute" time="0.0" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="controlsAddedAfterParseAreLinkedWithForms" time="0.0" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="adoptedFormsRetainInputs" time="0.0" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="usesOnForCheckboxValueIfNoValueSet" time="0.001" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="hasAssociatedControls" time="0.0" />

      <testcase classname="org.jsoup.nodes.FormElementTest" name="actionWithNoBaseUri" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="12" name="NodeTest" package="org.jsoup.nodes" skipped="0" tests="25" time="0.083" timestamp="2025-09-14T06:47:02">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.NodeTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.NodeTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3420966165427167592.properties propsfile=/Users/<USER>/Desktop/jsoup/junit5329414420675204152.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.NodeTest" name="handlesAbsOnProtocolessAbsoluteUris" time="0.026" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="setBaseUriIsRecursive" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="ownerDocument" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="before" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="handleAbsOnLocalhostFileUris" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="unwrap" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="absHandlesDotFromIndex" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="supportsClone" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="root" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="after" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="absHandlesRelativeQuery" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="unwrapNoChildren" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="literalAbsPrefix" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="handlesBaseUri" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="handlesAbsOnImage" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="nodeIsNotASiblingOfItself" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="handleAbsOnFileUris" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="testReplace" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="handlesAbsPrefixOnHasAttr" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="childNodesCopy" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="changingAttributeValueShouldReplaceExistingAttributeCaseInsensitive" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="traverse" time="0.001" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="orphanNodeReturnsNullForSiblingElements" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="handlesAbsPrefix" time="0.0" />

      <testcase classname="org.jsoup.nodes.NodeTest" name="testRemove" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="13" name="TextNodeTest" package="org.jsoup.nodes" skipped="0" tests="6" time="0.102" timestamp="2025-09-14T06:47:02">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.nodes.TextNodeTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.nodes.TextNodeTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher8958352721694346205.properties propsfile=/Users/<USER>/Desktop/jsoup/junit13128646035421819341.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.nodes.TextNodeTest" name="testWithSupplementaryCharacter" time="0.04" />

      <testcase classname="org.jsoup.nodes.TextNodeTest" name="testLeadNodesHaveNoChildren" time="0.004" />

      <testcase classname="org.jsoup.nodes.TextNodeTest" name="testBlank" time="0.001" />

      <testcase classname="org.jsoup.nodes.TextNodeTest" name="testTextBean" time="0.001" />

      <testcase classname="org.jsoup.nodes.TextNodeTest" name="testSplitText" time="0.0" />

      <testcase classname="org.jsoup.nodes.TextNodeTest" name="testSplitAnEmbolden" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="14" name="AttributeParseTest" package="org.jsoup.parser" skipped="0" tests="8" time="0.083" timestamp="2025-09-14T06:47:02">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.AttributeParseTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.AttributeParseTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher4173531065496173108.properties propsfile=/Users/<USER>/Desktop/jsoup/junit9254174489975818359.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="dropsSlashFromAttributeName" time="0.029" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="moreAttributeUnescapes" time="0.0" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="parsesBooleanAttributes" time="0.001" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="handlesNewLinesAndReturns" time="0.0" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="canStartWithEq" time="0.0" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="parsesRoughAttributeString" time="0.0" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="strictAttributeUnescapes" time="0.0" />

      <testcase classname="org.jsoup.parser.AttributeParseTest" name="parsesEmptyString" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="15" name="CharacterReaderTest" package="org.jsoup.parser" skipped="0" tests="23" time="0.053" timestamp="2025-09-14T06:47:03">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.CharacterReaderTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.CharacterReaderTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher12189985041506691792.properties propsfile=/Users/<USER>/Desktop/jsoup/junit9873767124928947060.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="nextIndexOfUnmatched" time="0.001" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="unconsume" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="bufferUp" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="nextIndexOfChar" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="notEmptyAtBufferSplitPoint" time="0.001" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="rangeEquals" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeToChar" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="advance" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeToString" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeToAny" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeToEnd" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeToNonexistentEndWhenAtAnd" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="containsIgnoreCase" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="mark" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="empty" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="cachesStrings" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeLetterThenDigitSequence" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consumeLetterSequence" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="nextIndexOfString" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="matches" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="consume" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="matchesIgnoreCase" time="0.0" />

      <testcase classname="org.jsoup.parser.CharacterReaderTest" name="matchesAny" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="16" name="HtmlParserTest" package="org.jsoup.parser" skipped="0" tests="132" time="0.333" timestamp="2025-09-14T06:47:03">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.HtmlParserTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.HtmlParserTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher2127338097888008035.properties propsfile=/Users/<USER>/Desktop/jsoup/junit11267010801563507682.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="preservedCaseLinksCantNest" time="0.023" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisesHeadlessBody" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesUnterminatedOption" time="0.002" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesProtocolRelativeUrl" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="dropsUnterminatedTag" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="canPreserveAttributeCase" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNullInComments" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTextAfterData" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="dropsUnterminatedAttribute" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testNormalisesIsIndex" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="noTableDirectInTable" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSupportsPartiallyNonAsciiTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSpaceAfterTag" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesXmlDeclarationAsBogusComment" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisesDocument" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="createsStructureFromBodySnippet" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="retainsAttributesOfDifferentCaseIfSensitive" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="canPreserveTagCase" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="preservesSpaceInScript" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesXmlDeclAndCommentsBeforeDoctype" time="0.002" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHtmlLowerCase" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesDeepStack" time="0.162" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesSimpleDocument" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="doesNotCreateImplicitLists" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesUnterminatedTextarea" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSupportsNonAsciiTags" time="0.005" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="dropsDuplicateAttributes" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesWhatWgExpensesTableExample" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesImplicitCaptionClose" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesQuotesInCommentsInScripts" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesInputInTable" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesInvalidStartTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesEscapedData" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="fallbackToUtfIfCantEncode" time="0.005" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="reconstructFormattingElements" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="createsDocumentStructure" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCdataInScript" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedTitle" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="caseInsensitiveParseTree" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handleNullContextInParseFragment" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesUnterminatedComments" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="emptyTdTag" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="caseSensitiveParseTree" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testFragment" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedScriptAtEof" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNewlinesAndWhitespaceInTag" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesDataOnlyTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalizesDiscordantTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCdataAcrossBuffer" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="noImplicitFormForTextAreas" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="commentBeforeHtml" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedCdataAtEOF" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCdata" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyBlocks" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="createsFormElements" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="noErrorsByDefault" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHeaderContents" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testRelaxedTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyStyle" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyTitle" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="preservesSpaceInTextArea" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnexpectedMarkupInTables" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyIframe" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="characterReaderBuffer" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testTemplateInsideTable" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="cdataNodesAreTextNodes" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="ignoresContentAfterFrameset" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesBaseTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSpanContents" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handles0CharacterAsText" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesFrames" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesJavadocFont" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testReinsertionModeForThCelss" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesControlCodeInAttributeName" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesInvalidDoctypes" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTagsInTextarea" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCommentsInTable" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesEscapedScript" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testInvalidTableContents" time="0.002" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnknownTags" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="ignoresDupeEndTrTag" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="associatedFormControlsWithDisjointForms" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTbodyTable" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisedBodyAfterContent" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesSpanInTbody" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedTitleAtEof" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesSolidusAtAttributeEnd" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnknownNamespaceTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyNoFrames" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="roundTripsCdata" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesManyChildren" time="0.025" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTextArea" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="convertsImageToImg" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="preSkipsFirstNewline" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="commentAtEnd" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="selfClosingOnNonvoidIsError" time="0.002" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNestedImplicitTable" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesRoughAttributes" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testUsingSingleQuotesInQueries" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedDefinitionLists" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesWhitespaceInoDocType" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="reconstructFormattingElementsInTable" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="selfClosingVoidIsNotAnError" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testNoImagesInNoScriptInHead" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesBaseWithoutHref" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedRawtextAtEof" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="tracksLimitedErrorsWhenRequested" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="doesNotFindShortestMatchingEntity" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="tracksErrorsWhenRequested" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedAnchors" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesBodyFragment" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnknownInlineTags" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSelectWithOption" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisesEmptyDocument" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="relaxedBaseEntityMatchAndStrictExtendedMatch" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedFormattingElements" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesSolidusInA" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHgroup" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="discardsNakedTds" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesComments" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testFontFlowContents" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesMisnestedTagsBI" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesMisnestedTagsBP" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="canPreserveBothCase" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHandlesDeepSpans" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handleCDataInText" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="findsCharsetInMalformedMeta" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesMultiClosingBody" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesBlocksInDefinitions" time="0.001" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="testAFlowContents" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNullInData" time="0.0" />

      <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesQuiteRoughAttributes" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="17" name="HtmlTreeBuilderStateTest" package="org.jsoup.parser" skipped="0" tests="1" time="0.05" timestamp="2025-09-14T06:47:03">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.HtmlTreeBuilderStateTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.HtmlTreeBuilderStateTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher16523465003265649485.properties propsfile=/Users/<USER>/Desktop/jsoup/junit3253883691111386848.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.HtmlTreeBuilderStateTest" name="ensureArraysAreSorted" time="0.002" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="18" name="HtmlTreeBuilderTest" package="org.jsoup.parser" skipped="0" tests="1" time="0.051" timestamp="2025-09-14T06:47:03">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.HtmlTreeBuilderTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.HtmlTreeBuilderTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3477354919847197444.properties propsfile=/Users/<USER>/Desktop/jsoup/junit14912773523344013802.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.HtmlTreeBuilderTest" name="ensureSearchArraysAreSorted" time="0.003" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="19" name="ParserSettingsTest" package="org.jsoup.parser" skipped="0" tests="3" time="0.053" timestamp="2025-09-14T06:47:04">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.ParserSettingsTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.ParserSettingsTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher4658428271143218207.properties propsfile=/Users/<USER>/Desktop/jsoup/junit15526034357869689449.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.ParserSettingsTest" name="attributesCaseNormalization" time="0.002" />

      <testcase classname="org.jsoup.parser.ParserSettingsTest" name="caseSupport" time="0.001" />

      <testcase classname="org.jsoup.parser.ParserSettingsTest" name="attributeCaseNormalization" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="20" name="ParserTest" package="org.jsoup.parser" skipped="0" tests="2" time="0.067" timestamp="2025-09-14T06:47:04">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.ParserTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.ParserTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher12821440818733057239.properties propsfile=/Users/<USER>/Desktop/jsoup/junit8783140115428053925.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.ParserTest" name="unescapeEntitiesHandlesLargeInput" time="0.011" />

      <testcase classname="org.jsoup.parser.ParserTest" name="unescapeEntities" time="0.007" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="21" name="TagTest" package="org.jsoup.parser" skipped="0" tests="10" time="0.054" timestamp="2025-09-14T06:47:04">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.TagTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.TagTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher12369822178455029320.properties propsfile=/Users/<USER>/Desktop/jsoup/junit7380496697040948793.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.TagTest" name="divSemantics" time="0.002" />

      <testcase classname="org.jsoup.parser.TagTest" name="trims" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="imgSemantics" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="valueOfChecksNotEmpty" time="0.001" />

      <testcase classname="org.jsoup.parser.TagTest" name="pSemantics" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="equality" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="defaultSemantics" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="canBeInsensitive" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="valueOfChecksNotNull" time="0.0" />

      <testcase classname="org.jsoup.parser.TagTest" name="isCaseSensitive" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="22" name="TokenQueueTest" package="org.jsoup.parser" skipped="0" tests="9" time="0.078" timestamp="2025-09-14T06:47:04">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.TokenQueueTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.TokenQueueTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher13745704542501935947.properties propsfile=/Users/<USER>/Desktop/jsoup/junit11697125808876920248.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="addFirst" time="0.001" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="unescape" time="0.001" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="chompToIgnoreCase" time="0.0" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="chompEscapedBalanced" time="0.0" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="consumeToIgnoreSecondCallTest" time="0.0" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="chompBalancedThrowIllegalArgumentException" time="0.0" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="chompBalanced" time="0.001" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="testNestedQuotes" time="0.025" />

      <testcase classname="org.jsoup.parser.TokenQueueTest" name="chompBalancedMatchesAsMuchAsPossible" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="23" name="TokeniserStateTest" package="org.jsoup.parser" skipped="0" tests="12" time="0.091" timestamp="2025-09-14T06:47:04">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.TokeniserStateTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.TokeniserStateTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher11390795090201576615.properties propsfile=/Users/<USER>/Desktop/jsoup/junit7471609708175559120.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testEndTagOpen" time="0.027" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testPublicIdentifiersWithWhitespace" time="0.006" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="handlesLessInTagThanAsNewTag" time="0.001" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testRCDATAEndTagName" time="0.001" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testCharacterReferenceInRcdata" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testRcdataLessthanSign" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="ensureSearchArraysAreSorted" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testCommentEndCoverage" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testBeforeTagName" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testPublicAndSystemIdentifiersWithWhitespace" time="0.001" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testSystemIdentifiersWithWhitespace" time="0.003" />

      <testcase classname="org.jsoup.parser.TokeniserStateTest" name="testCommentEndBangCoverage" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="24" name="TokeniserTest" package="org.jsoup.parser" skipped="0" tests="10" time="0.104" timestamp="2025-09-14T06:47:05">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.TokeniserTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.TokeniserTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher12130262619675658576.properties propsfile=/Users/<USER>/Desktop/jsoup/junit1221495004130848088.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.TokeniserTest" name="handleLargeComment" time="0.027" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="handleLargeCdata" time="0.001" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="handleLargeTitle" time="0.005" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="bufferUpInAttributeVal" time="0.005" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="cp1252EntitiesProduceError" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="handleSuperLargeTagNames" time="0.006" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="handleSuperLargeAttributeName" time="0.001" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="cp1252SubstitutionTable" time="0.0" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="cp1252Entities" time="0.001" />

      <testcase classname="org.jsoup.parser.TokeniserTest" name="handleLargeText" time="0.005" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="25" name="XmlTreeBuilderTest" package="org.jsoup.parser" skipped="0" tests="23" time="0.085" timestamp="2025-09-14T06:47:05">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.XmlTreeBuilderTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.XmlTreeBuilderTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher7260684078011642916.properties propsfile=/Users/<USER>/Desktop/jsoup/junit5132712622548024069.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testPopToClose" time="0.021" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="handlesXmlDeclarationAsDeclaration" time="0.001" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="dropsDuplicateAttributes" time="0.002" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="caseSensitiveDeclaration" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="handlesDodgyXmlDecl" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="handlesLTinScript" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testSupplyParserToJsoupClass" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="normalizesDiscordantTags" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testDoesNotForceSelfClosingKnownTags" time="0.003" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testCommentAndDocType" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testSimpleXmlParse" time="0.001" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="xmlFragment" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="appendPreservesCaseByDefault" time="0.001" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testDetectCharsetEncodingDeclaration" time="0.002" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="roundTripsCdata" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="canNormalizeCase" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="cdataPreservesWhiteSpace" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="preservesCaseByDefault" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testParseDeclarationAttributes" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testSupplyParserToDataStream" time="0.001" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="xmlParseDefaultsToHtmlOutputSyntax" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testCreatesValidProlog" time="0.0" />

      <testcase classname="org.jsoup.parser.XmlTreeBuilderTest" name="testDoesHandleEOFInTag" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="26" name="CleanerTest" package="org.jsoup.safety" skipped="0" tests="34" time="0.092" timestamp="2025-09-14T06:47:05">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.safety.CleanerTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.safety.CleanerTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher8726457367736268269.properties propsfile=/Users/<USER>/Desktop/jsoup/junit11510070941490349763.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.safety.CleanerTest" name="testHandlesEmptyAttributes" time="0.025" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="basicBehaviourTest" time="0.002" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="handlesCustomProtocols" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testIsValidBodyHtml" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="handlesNoHrefAttribute" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="preservesRelativeLinksIfConfigured" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testCleanAnchorProtocol" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="bailsIfRemovingProtocolThatsNotSet" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testDropScript" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testRemoveTags" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testRemoveAttributes" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testRemoveEnforcedAttributes" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testRelaxed" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="resolvesRelativeLinks" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testDropComments" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="handlesFramesets" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="handlesControlCharactersAfterTagName" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="simpleBehaviourTest2" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testRemoveProtocols" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testDropImageScript" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="simpleBehaviourTest" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="handlesAttributesWithNoValue" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="dropsUnresolvableRelativeLinks" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testScriptTagInWhiteList" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="addsTagOnAttributesIfNotSet" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testDropsUnknownTags" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testCleanJavascriptHref" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="basicWithImagesTest" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="whitelistedProtocolShouldBeRetained" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testDropXmlProc" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="testIsValidDocument" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="handlesAllPseudoTag" time="0.001" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="supplyOutputSettings" time="0.0" />

      <testcase classname="org.jsoup.safety.CleanerTest" name="cleansInternationalText" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="27" name="CssTest" package="org.jsoup.select" skipped="0" tests="17" time="0.117" timestamp="2025-09-14T06:47:05">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.select.CssTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.select.CssTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher9580031341889992662.properties propsfile=/Users/<USER>/Desktop/jsoup/junit11565228029130907142.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.select.CssTest" name="nthLastOfType_advanced" time="0.028" />

      <testcase classname="org.jsoup.select.CssTest" name="nthChild_advanced" time="0.002" />

      <testcase classname="org.jsoup.select.CssTest" name="nthOfType_advanced" time="0.002" />

      <testcase classname="org.jsoup.select.CssTest" name="nthOfType_simple" time="0.002" />

      <testcase classname="org.jsoup.select.CssTest" name="firstOfType" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="firstChild" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="nthLastOfType_simple" time="0.002" />

      <testcase classname="org.jsoup.select.CssTest" name="root" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="onlyOfType" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="nthLastChild_simple" time="0.002" />

      <testcase classname="org.jsoup.select.CssTest" name="empty" time="0.005" />

      <testcase classname="org.jsoup.select.CssTest" name="onlyChild" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="nthChild_simple" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="nthLastChild_advanced" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="lastOfType" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="lastChild" time="0.001" />

      <testcase classname="org.jsoup.select.CssTest" name="nthOfType_unknownTag" time="0.002" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="28" name="ElementsTest" package="org.jsoup.select" skipped="0" tests="34" time="0.094" timestamp="2025-09-14T06:47:06">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.select.ElementsTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.select.ElementsTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher835079832014892863.properties propsfile=/Users/<USER>/Desktop/jsoup/junit3359298020214751451.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.select.ElementsTest" name="classWithHyphen" time="0.025" />

      <testcase classname="org.jsoup.select.ElementsTest" name="before" time="0.002" />

      <testcase classname="org.jsoup.select.ElementsTest" name="filter" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="eachAttr" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="eachText" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="absAttr" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="remove" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="unwrap" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="parents" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="unwrapP" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="unwrapKeepsSpace" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="eq" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="is" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="not" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="val" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="attr" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="html" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="text" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="wrap" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="hasClassCaseInsensitive" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="after" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="empty" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="forms" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="siblings" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="attributes" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="hasAttr" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="hasText" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="hasAbsAttr" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="classes" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="traverse" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="wrapDiv" time="0.001" />

      <testcase classname="org.jsoup.select.ElementsTest" name="outerHtml" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="tagNameSet" time="0.0" />

      <testcase classname="org.jsoup.select.ElementsTest" name="setHtml" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="29" name="QueryParserTest" package="org.jsoup.select" skipped="0" tests="4" time="0.054" timestamp="2025-09-14T06:47:06">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.select.QueryParserTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.select.QueryParserTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher7699429407353307688.properties propsfile=/Users/<USER>/Desktop/jsoup/junit10948532299009583955.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.select.QueryParserTest" name="testParsesSingleQuoteInContains" time="0.003" />

      <testcase classname="org.jsoup.select.QueryParserTest" name="testOrGetsCorrectPrecedence" time="0.0" />

      <testcase classname="org.jsoup.select.QueryParserTest" name="testParsesMultiCorrectly" time="0.001" />

      <testcase classname="org.jsoup.select.QueryParserTest" name="exceptionOnUncloseAttribute" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="30" name="SelectorTest" package="org.jsoup.select" skipped="0" tests="62" time="0.114" timestamp="2025-09-14T06:47:06">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.select.SelectorTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.select.SelectorTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3309209341142844459.properties propsfile=/Users/<USER>/Desktop/jsoup/junit9169315804021469307.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.select.SelectorTest" name="containsData" time="0.026" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByAttributeStarting" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="deeperDescendant" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="selectSameElements" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoEquals" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByClassCaseInsensitive" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByAttributeRegexCharacterClass" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testAllWithClass" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByTag" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testById" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="findBetweenSpan" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="descendant" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="handlesCommasInSelector" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="notAll" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByAttribute" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="selectClassWithSpace" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="notAdjacent" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testCombinedWithContains" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="containsWithQuote" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoGreaterThan" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testAllElements" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByAttributeRegex" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="mixCombinator" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testRelaxedTags" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoBetween" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoLessThan" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testGroupOr" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByClass" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="and" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="attributeWithBrackets" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPsuedoContainsWithParentheses" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testGroupOrAttribute" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="caseInsensitive" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="selectFirst" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="containsOwn" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testCharactersInIdAndClass" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="splitOnBr" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="multiChildDescent" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="adjacentSiblingsWithId" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testMatches" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="matchText" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="selectFirstWithOr" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="matchTextAttributes" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="parentWithClassChild" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="generalSiblings" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="parentChildStar" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoCombined" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoContains" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testPseudoHas" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="notClass" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="notParas" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="selectSupplementaryCharacter" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="adjacentSiblings" time="0.002" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testNamespacedTag" time="0.002" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testNestedHas" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="parentChildElement" time="0.0" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testByAttributeRegexCombined" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="matchesOwn" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testSupportsLeadingCombinator" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="mixCombinatorGroup" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="testWildcardNamespacedTag" time="0.001" />

      <testcase classname="org.jsoup.select.SelectorTest" name="selectFirstWithAnd" time="0.001" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
  <testsuite errors="0" failures="0" hostname="Justins-MacBook-Air-1030.local" id="31" name="TraversorTest" package="org.jsoup.select" skipped="0" tests="5" time="0.075" timestamp="2025-09-14T06:47:06">
      <properties>
          <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />

          <property name="env.TERM" value="xterm-256color" />

          <property name="java.specification.version" value="23" />

          <property name="ant.project.name" value="jsoup" />

          <property name="sun.jnu.encoding" value="UTF-8" />

          <property name="sun.arch.data.model" value="64" />

          <property name="debuglevel" value="source,lines,vars" />

          <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />

          <property name="env.GIT_PAGER" value="cat" />

          <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="env.RUBYOPT" value="" />

          <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />

          <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.select.TraversorTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.select.TraversorTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher1314889436046235770.properties propsfile=/Users/<USER>/Desktop/jsoup/junit1817960601468270406.properties" />

          <property name="jdk.debug" value="release" />

          <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />

          <property name="java.specification.vendor" value="Oracle Corporation" />

          <property name="java.version.date" value="2025-01-21" />

          <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />

          <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />

          <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="file.separator" value="/" />

          <property name="env.LESS" value="-FX" />

          <property name="java.vm.compressedOopsMode" value="Zero based" />

          <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />

          <property name="line.separator" value="&#xa;" />

          <property name="java.specification.name" value="Java Platform API Specification" />

          <property name="java.vm.specification.vendor" value="Oracle Corporation" />

          <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />

          <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />

          <property name="java.runtime.version" value="23.0.2" />

          <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />

          <property name="user.name" value="justin" />

          <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />

          <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />

          <property name="env.XPC_FLAGS" value="0x0" />

          <property name="env.LOGNAME" value="justin" />

          <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />

          <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />

          <property name="file.encoding" value="UTF-8" />

          <property name="java.vendor.version" value="Homebrew" />

          <property name="env.SHLVL" value="2" />

          <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="java.version" value="23.0.2" />

          <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />

          <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />

          <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />

          <property name="junit.output.dir" value="ant-target/reports" />

          <property name="native.encoding" value="UTF-8" />

          <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />

          <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />

          <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />

          <property name="stderr.encoding" value="UTF-8" />

          <property name="java.vendor" value="Homebrew" />

          <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="sun.io.unicode.encoding" value="UnicodeBig" />

          <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />

          <property name="env.TERM_PROGRAM" value="vscode" />

          <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="ant.file.type" value="file" />

          <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />

          <property name="env.LANG" value="en_US.UTF-8" />

          <property name="env.PAGER" value="cat" />

          <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />

          <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />

          <property name="env.HOME" value="/Users/<USER>" />

          <property name="env.COMMAND_MODE" value="unix2003" />

          <property name="source" value="1.8" />

          <property name="java.vm.vendor" value="Homebrew" />

          <property name="ant.file.type.jsoup" value="file" />

          <property name="java.vm.specification.version" value="23" />

          <property name="os.name" value="Mac OS X" />

          <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />

          <property name="env.RUBY_ENGINE" value="ruby" />

          <property name="sun.java.launcher" value="SUN_STANDARD" />

          <property name="user.country" value="US" />

          <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="sun.cpu.endian" value="little" />

          <property name="user.home" value="/Users/<USER>" />

          <property name="user.language" value="en" />

          <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />

          <property name="env.COLORTERM" value="truecolor" />

          <property name="ant.java.version" value="23" />

          <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />

          <property name="apple.awt.application.name" value="JUnitTestRunner" />

          <property name="env.XPC_SERVICE_NAME" value="0" />

          <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />

          <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />

          <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />

          <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />

          <property name="stdout.encoding" value="UTF-8" />

          <property name="path.separator" value=":" />

          <property name="env.RUBY_VERSION" value="3.4.1" />

          <property name="os.version" value="15.6.1" />

          <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />

          <property name="java.runtime.name" value="OpenJDK Runtime Environment" />

          <property name="ant.project.invoked-targets" value="clean,test" />

          <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />

          <property name="env.SHELL" value="/bin/bash" />

          <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />

          <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />

          <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />

          <property name="os.arch" value="aarch64" />

          <property name="env.MallocNanoZone" value="0" />

          <property name="target" value="1.8" />

          <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />

          <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />

          <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />

          <property name="java.vm.info" value="mixed mode, sharing" />

          <property name="java.vm.version" value="23.0.2" />

          <property name="env.USER" value="justin" />

          <property name="java.class.version" value="67.0" />

          <property name="ant.project.default-target" value="build" />

      </properties>

      <testcase classname="org.jsoup.select.TraversorTest" name="filterStop" time="0.026" />

      <testcase classname="org.jsoup.select.TraversorTest" name="filterVisit" time="0.001" />

      <testcase classname="org.jsoup.select.TraversorTest" name="filterSkipChildren" time="0.0" />

      <testcase classname="org.jsoup.select.TraversorTest" name="filterSkipEntirely" time="0.001" />

      <testcase classname="org.jsoup.select.TraversorTest" name="filterRemove" time="0.0" />

      <system-out><![CDATA[]]></system-out>

      <system-err><![CDATA[]]></system-err>

  </testsuite>
</testsuites>
