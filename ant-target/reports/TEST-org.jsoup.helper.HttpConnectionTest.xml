<?xml version="1.0" encoding="UTF-8" ?>
<testsuite errors="0" failures="0" hostname="Justin<PERSON>-MacBook-Air-1030.local" name="org.jsoup.helper.HttpConnectionTest" skipped="0" tests="23" time="0.072" timestamp="2025-09-14T06:46:59">
  <properties>
    <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />
    <property name="env.TERM" value="xterm-256color" />
    <property name="java.specification.version" value="23" />
    <property name="ant.project.name" value="jsoup" />
    <property name="sun.jnu.encoding" value="UTF-8" />
    <property name="sun.arch.data.model" value="64" />
    <property name="debuglevel" value="source,lines,vars" />
    <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />
    <property name="env.GIT_PAGER" value="cat" />
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />
    <property name="env.RUBYOPT" value="" />
    <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />
    <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.helper.HttpConnectionTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.helper.HttpConnectionTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher14718646934270915654.properties propsfile=/Users/<USER>/Desktop/jsoup/junit3336693168029679784.properties" />
    <property name="jdk.debug" value="release" />
    <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />
    <property name="java.specification.vendor" value="Oracle Corporation" />
    <property name="java.version.date" value="2025-01-21" />
    <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />
    <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />
    <property name="file.separator" value="/" />
    <property name="env.LESS" value="-FX" />
    <property name="java.vm.compressedOopsMode" value="Zero based" />
    <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />
    <property name="line.separator" value="&#xa;" />
    <property name="java.specification.name" value="Java Platform API Specification" />
    <property name="java.vm.specification.vendor" value="Oracle Corporation" />
    <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />
    <property name="java.runtime.version" value="23.0.2" />
    <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />
    <property name="user.name" value="justin" />
    <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />
    <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />
    <property name="env.XPC_FLAGS" value="0x0" />
    <property name="env.LOGNAME" value="justin" />
    <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />
    <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />
    <property name="file.encoding" value="UTF-8" />
    <property name="java.vendor.version" value="Homebrew" />
    <property name="env.SHLVL" value="2" />
    <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />
    <property name="java.version" value="23.0.2" />
    <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />
    <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />
    <property name="junit.output.dir" value="ant-target/reports" />
    <property name="native.encoding" value="UTF-8" />
    <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />
    <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />
    <property name="stderr.encoding" value="UTF-8" />
    <property name="java.vendor" value="Homebrew" />
    <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />
    <property name="sun.io.unicode.encoding" value="UnicodeBig" />
    <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />
    <property name="env.TERM_PROGRAM" value="vscode" />
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />
    <property name="ant.file.type" value="file" />
    <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />
    <property name="env.LANG" value="en_US.UTF-8" />
    <property name="env.PAGER" value="cat" />
    <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />
    <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />
    <property name="env.HOME" value="/Users/<USER>" />
    <property name="env.COMMAND_MODE" value="unix2003" />
    <property name="source" value="1.8" />
    <property name="java.vm.vendor" value="Homebrew" />
    <property name="ant.file.type.jsoup" value="file" />
    <property name="java.vm.specification.version" value="23" />
    <property name="os.name" value="Mac OS X" />
    <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />
    <property name="env.RUBY_ENGINE" value="ruby" />
    <property name="sun.java.launcher" value="SUN_STANDARD" />
    <property name="user.country" value="US" />
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />
    <property name="sun.cpu.endian" value="little" />
    <property name="user.home" value="/Users/<USER>" />
    <property name="user.language" value="en" />
    <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />
    <property name="env.COLORTERM" value="truecolor" />
    <property name="ant.java.version" value="23" />
    <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />
    <property name="apple.awt.application.name" value="JUnitTestRunner" />
    <property name="env.XPC_SERVICE_NAME" value="0" />
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />
    <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />
    <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />
    <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />
    <property name="stdout.encoding" value="UTF-8" />
    <property name="path.separator" value=":" />
    <property name="env.RUBY_VERSION" value="3.4.1" />
    <property name="os.version" value="15.6.1" />
    <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />
    <property name="java.runtime.name" value="OpenJDK Runtime Environment" />
    <property name="ant.project.invoked-targets" value="clean,test" />
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />
    <property name="env.SHELL" value="/bin/bash" />
    <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />
    <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />
    <property name="os.arch" value="aarch64" />
    <property name="env.MallocNanoZone" value="0" />
    <property name="target" value="1.8" />
    <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />
    <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />
    <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />
    <property name="java.vm.info" value="mixed mode, sharing" />
    <property name="java.vm.version" value="23.0.2" />
    <property name="env.USER" value="justin" />
    <property name="java.class.version" value="67.0" />
    <property name="ant.project.default-target" value="build" />
  </properties>
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsExceptionOnParseWithoutExecute" time="0.009" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="connectWithUrl" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="ignoresEmptySetCookies" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsOnMalformedUrl" time="0.001" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="multipleHeaders" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="cookie" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="timeout" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="method" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="handlesHeaderEncodingOnRequest" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="referrer" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="sameHeadersCombineWithComma" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="inputStream" time="0.001" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsExceptionOnBodyAsBytesWithoutExecute" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="data" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="userAgent" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsExceptionOnBodyWithoutExecute" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="headers" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="noUrlThrowsValidationError" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="requestBody" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="ignoresEmptyCookieNameAndVals" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="encodeUrl" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="throwsOnOddData" time="0.0" />
  <testcase classname="org.jsoup.helper.HttpConnectionTest" name="caseInsensitiveHeaders" time="0.0" />
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
