<?xml version="1.0" encoding="UTF-8" ?>
<testsuite errors="0" failures="0" hostname="Justin<PERSON>-MacBook-Air-1030.local" name="org.jsoup.parser.HtmlParserTest" skipped="0" tests="132" time="0.333" timestamp="2025-09-14T06:47:03">
  <properties>
    <property name="ant.library.dir" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib" />
    <property name="env.TERM" value="xterm-256color" />
    <property name="java.specification.version" value="23" />
    <property name="ant.project.name" value="jsoup" />
    <property name="sun.jnu.encoding" value="UTF-8" />
    <property name="sun.arch.data.model" value="64" />
    <property name="debuglevel" value="source,lines,vars" />
    <property name="env.GEM_HOME" value="/Users/<USER>/.gem/ruby/3.4.1" />
    <property name="env.GIT_PAGER" value="cat" />
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues" />
    <property name="env.RUBYOPT" value="" />
    <property name="env.GEM_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib" />
    <property name="sun.java.command" value="org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.HtmlParserTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.HtmlParserTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher2127338097888008035.properties propsfile=/Users/<USER>/Desktop/jsoup/junit11267010801563507682.properties" />
    <property name="jdk.debug" value="release" />
    <property name="env.VSCODE_GIT_ASKPASS_MAIN" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js" />
    <property name="java.specification.vendor" value="Oracle Corporation" />
    <property name="java.version.date" value="2025-01-21" />
    <property name="env.JAVA_HOME" value="/opt/homebrew/opt/openjdk" />
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home" />
    <property name="basedir" value="/Users/<USER>/Desktop/jsoup" />
    <property name="file.separator" value="/" />
    <property name="env.LESS" value="-FX" />
    <property name="java.vm.compressedOopsMode" value="Zero based" />
    <property name="env.GIT_ASKPASS" value="/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh" />
    <property name="line.separator" value="&#xa;" />
    <property name="java.specification.name" value="Java Platform API Specification" />
    <property name="java.vm.specification.vendor" value="Oracle Corporation" />
    <property name="env.HOMEBREW_REPOSITORY" value="/opt/homebrew" />
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers" />
    <property name="java.runtime.version" value="23.0.2" />
    <property name="env.TERM_PROGRAM_VERSION" value="1.103.2" />
    <property name="user.name" value="justin" />
    <property name="env.PATH" value="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts" />
    <property name="env.ORIGINAL_XDG_CURRENT_DESKTOP" value="undefined" />
    <property name="env.XPC_FLAGS" value="0x0" />
    <property name="env.LOGNAME" value="justin" />
    <property name="env.__CF_USER_TEXT_ENCODING" value="0x1F5:0x0:0x0" />
    <property name="env.__CFBundleIdentifier" value="com.microsoft.VSCode" />
    <property name="file.encoding" value="UTF-8" />
    <property name="java.vendor.version" value="Homebrew" />
    <property name="env.SHLVL" value="2" />
    <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />
    <property name="java.version" value="23.0.2" />
    <property name="env.SSH_AUTH_SOCK" value="/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners" />
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification" />
    <property name="ant.home" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec" />
    <property name="junit.output.dir" value="ant-target/reports" />
    <property name="native.encoding" value="UTF-8" />
    <property name="ant.version" value="Apache Ant(TM) version 1.10.15 compiled on August 25 2024" />
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:." />
    <property name="env.TMPDIR" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/" />
    <property name="stderr.encoding" value="UTF-8" />
    <property name="java.vendor" value="Homebrew" />
    <property name="ant.file.jsoup" value="/Users/<USER>/Desktop/jsoup/build.xml" />
    <property name="sun.io.unicode.encoding" value="UnicodeBig" />
    <property name="env.INFOPATH" value="/opt/homebrew/share/info:" />
    <property name="env.TERM_PROGRAM" value="vscode" />
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />
    <property name="ant.file.type" value="file" />
    <property name="env.PWD" value="/Users/<USER>/Desktop/jsoup" />
    <property name="env.LANG" value="en_US.UTF-8" />
    <property name="env.PAGER" value="cat" />
    <property name="env.RUBY_ROOT" value="/Users/<USER>/.rubies/ruby-3.4.1" />
    <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar" />
    <property name="env.HOME" value="/Users/<USER>" />
    <property name="env.COMMAND_MODE" value="unix2003" />
    <property name="source" value="1.8" />
    <property name="java.vm.vendor" value="Homebrew" />
    <property name="ant.file.type.jsoup" value="file" />
    <property name="java.vm.specification.version" value="23" />
    <property name="os.name" value="Mac OS X" />
    <property name="env.DISPLAY" value="/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0" />
    <property name="env.RUBY_ENGINE" value="ruby" />
    <property name="sun.java.launcher" value="SUN_STANDARD" />
    <property name="user.country" value="US" />
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />
    <property name="sun.cpu.endian" value="little" />
    <property name="user.home" value="/Users/<USER>" />
    <property name="user.language" value="en" />
    <property name="env.VSCODE_GIT_IPC_HANDLE" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock" />
    <property name="env.COLORTERM" value="truecolor" />
    <property name="ant.java.version" value="23" />
    <property name="env.HOMEBREW_CELLAR" value="/opt/homebrew/Cellar" />
    <property name="apple.awt.application.name" value="JUnitTestRunner" />
    <property name="env.XPC_SERVICE_NAME" value="0" />
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16" />
    <property name="env.VSCODE_GIT_ASKPASS_EXTRA_ARGS" value="" />
    <property name="env.HOMEBREW_PREFIX" value="/opt/homebrew" />
    <property name="ant.file" value="/Users/<USER>/Desktop/jsoup/build.xml" />
    <property name="stdout.encoding" value="UTF-8" />
    <property name="path.separator" value=":" />
    <property name="env.RUBY_VERSION" value="3.4.1" />
    <property name="os.version" value="15.6.1" />
    <property name="env.GEM_PATH" value="/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0" />
    <property name="java.runtime.name" value="OpenJDK Runtime Environment" />
    <property name="ant.project.invoked-targets" value="clean,test" />
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM" />
    <property name="env.SHELL" value="/bin/bash" />
    <property name="ant.core.lib" value="/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar" />
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues" />
    <property name="user.dir" value="/Users/<USER>/Desktop/jsoup" />
    <property name="os.arch" value="aarch64" />
    <property name="env.MallocNanoZone" value="0" />
    <property name="target" value="1.8" />
    <property name="env.SCRIPT" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log" />
    <property name="env.PROMPT_COMMAND" value="echo &quot;$(pwd)&quot; &gt; &apos;/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt&apos;&#xa;__augment_output_end_marker" />
    <property name="env.VSCODE_GIT_ASKPASS_NODE" value="/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)" />
    <property name="java.vm.info" value="mixed mode, sharing" />
    <property name="java.vm.version" value="23.0.2" />
    <property name="env.USER" value="justin" />
    <property name="java.class.version" value="67.0" />
    <property name="ant.project.default-target" value="build" />
  </properties>
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="preservedCaseLinksCantNest" time="0.023" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisesHeadlessBody" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesUnterminatedOption" time="0.002" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesProtocolRelativeUrl" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="dropsUnterminatedTag" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="canPreserveAttributeCase" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNullInComments" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTextAfterData" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="dropsUnterminatedAttribute" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testNormalisesIsIndex" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="noTableDirectInTable" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSupportsPartiallyNonAsciiTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSpaceAfterTag" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesXmlDeclarationAsBogusComment" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisesDocument" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="createsStructureFromBodySnippet" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="retainsAttributesOfDifferentCaseIfSensitive" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="canPreserveTagCase" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="preservesSpaceInScript" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesXmlDeclAndCommentsBeforeDoctype" time="0.002" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHtmlLowerCase" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesDeepStack" time="0.162" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesSimpleDocument" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="doesNotCreateImplicitLists" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesUnterminatedTextarea" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSupportsNonAsciiTags" time="0.005" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="dropsDuplicateAttributes" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesWhatWgExpensesTableExample" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesImplicitCaptionClose" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesQuotesInCommentsInScripts" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesInputInTable" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesInvalidStartTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesEscapedData" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="fallbackToUtfIfCantEncode" time="0.005" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="reconstructFormattingElements" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="createsDocumentStructure" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCdataInScript" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedTitle" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="caseInsensitiveParseTree" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handleNullContextInParseFragment" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesUnterminatedComments" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="emptyTdTag" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="caseSensitiveParseTree" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testFragment" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedScriptAtEof" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNewlinesAndWhitespaceInTag" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesDataOnlyTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalizesDiscordantTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCdataAcrossBuffer" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="noImplicitFormForTextAreas" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="commentBeforeHtml" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedCdataAtEOF" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCdata" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyBlocks" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="createsFormElements" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="noErrorsByDefault" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHeaderContents" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testRelaxedTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyStyle" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyTitle" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="preservesSpaceInTextArea" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnexpectedMarkupInTables" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyIframe" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="characterReaderBuffer" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testTemplateInsideTable" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="cdataNodesAreTextNodes" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="ignoresContentAfterFrameset" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesBaseTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSpanContents" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handles0CharacterAsText" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesFrames" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesJavadocFont" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testReinsertionModeForThCelss" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesControlCodeInAttributeName" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesInvalidDoctypes" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTagsInTextarea" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesCommentsInTable" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesEscapedScript" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testInvalidTableContents" time="0.002" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnknownTags" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="ignoresDupeEndTrTag" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="associatedFormControlsWithDisjointForms" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTbodyTable" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisedBodyAfterContent" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesSpanInTbody" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedTitleAtEof" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesSolidusAtAttributeEnd" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnknownNamespaceTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesKnownEmptyNoFrames" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="roundTripsCdata" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesManyChildren" time="0.025" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesTextArea" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="convertsImageToImg" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="preSkipsFirstNewline" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="commentAtEnd" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="selfClosingOnNonvoidIsError" time="0.002" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNestedImplicitTable" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesRoughAttributes" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testUsingSingleQuotesInQueries" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedDefinitionLists" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesWhitespaceInoDocType" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="reconstructFormattingElementsInTable" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="selfClosingVoidIsNotAnError" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testNoImagesInNoScriptInHead" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesBaseWithoutHref" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedRawtextAtEof" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="tracksLimitedErrorsWhenRequested" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="doesNotFindShortestMatchingEntity" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="tracksErrorsWhenRequested" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedAnchors" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesBodyFragment" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnknownInlineTags" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testSelectWithOption" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="normalisesEmptyDocument" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="relaxedBaseEntityMatchAndStrictExtendedMatch" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesUnclosedFormattingElements" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesSolidusInA" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHgroup" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="discardsNakedTds" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesComments" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testFontFlowContents" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesMisnestedTagsBI" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesMisnestedTagsBP" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="canPreserveBothCase" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testHandlesDeepSpans" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handleCDataInText" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="findsCharsetInMalformedMeta" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesMultiClosingBody" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesBlocksInDefinitions" time="0.001" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="testAFlowContents" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="handlesNullInData" time="0.0" />
  <testcase classname="org.jsoup.parser.HtmlParserTest" name="parsesQuiteRoughAttributes" time="0.0" />
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
