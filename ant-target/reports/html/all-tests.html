<html xmlns:string="xalan://java.lang.String" xmlns:lxslt="http://xml.apache.org/xslt">
    <head>
        <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Unit Test Results: All Tests</title>
        <link rel="stylesheet" type="text/css" title="Style" href="stylesheet.css">
    </head>
    <body onload="open('allclasses-frame.html','classListFrame')">
        <h1>Unit Test Results.</h1>
        <table width="100%">
            <tr>
                <td align="left"></td><td align="right">Designed for use with <a href="https://www.junit.org/">JUnit</a> and <a href="https://ant.apache.org/">Ant</a>.</td>
            </tr>
        </table>
        <hr size="1">
        <h2>All Tests</h2>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th>Class</th><th>Name</th><th>Status</th><th width="80%">Type</th><th nowrap>Time(s)</th>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="firstMetaElementWithCharsetShouldBeUsedForDecoding"></a><a href="org/jsoup/helper/0_DataUtilTest.html#firstMetaElementWithCharsetShouldBeUsedForDecoding">firstMetaElementWithCharsetShouldBeUsedForDecoding</a></td><td>Success</td><td></td><td>0.029</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="secondMetaElementWithContentTypeContainsCharsetParameter"></a><a href="org/jsoup/helper/0_DataUtilTest.html#secondMetaElementWithContentTypeContainsCharsetParameter">secondMetaElementWithContentTypeContainsCharsetParameter</a></td><td>Success</td><td></td><td>0.006</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="supportsUTF8BOM"></a><a href="org/jsoup/helper/0_DataUtilTest.html#supportsUTF8BOM">supportsUTF8BOM</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="generatesMimeBoundaries"></a><a href="org/jsoup/helper/0_DataUtilTest.html#generatesMimeBoundaries">generatesMimeBoundaries</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="discardsSpuriousByteOrderMarkWhenNoCharsetSet"></a><a href="org/jsoup/helper/0_DataUtilTest.html#discardsSpuriousByteOrderMarkWhenNoCharsetSet">discardsSpuriousByteOrderMarkWhenNoCharsetSet</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="discardsSpuriousByteOrderMark"></a><a href="org/jsoup/helper/0_DataUtilTest.html#discardsSpuriousByteOrderMark">discardsSpuriousByteOrderMark</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="shouldCorrectCharsetForDuplicateCharsetString"></a><a href="org/jsoup/helper/0_DataUtilTest.html#shouldCorrectCharsetForDuplicateCharsetString">shouldCorrectCharsetForDuplicateCharsetString</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="shouldNotThrowExceptionOnEmptyCharset"></a><a href="org/jsoup/helper/0_DataUtilTest.html#shouldNotThrowExceptionOnEmptyCharset">shouldNotThrowExceptionOnEmptyCharset</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="testCharset"></a><a href="org/jsoup/helper/0_DataUtilTest.html#testCharset">testCharset</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="supportsBOMinFiles"></a><a href="org/jsoup/helper/0_DataUtilTest.html#supportsBOMinFiles">supportsBOMinFiles</a></td><td>Success</td><td></td><td>0.004</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="testQuotedCharset"></a><a href="org/jsoup/helper/0_DataUtilTest.html#testQuotedCharset">testQuotedCharset</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="wrongMetaCharsetFallback"></a><a href="org/jsoup/helper/0_DataUtilTest.html#wrongMetaCharsetFallback">wrongMetaCharsetFallback</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="shouldSelectFirstCharsetOnWeirdMultileCharsetsInMetaTags"></a><a href="org/jsoup/helper/0_DataUtilTest.html#shouldSelectFirstCharsetOnWeirdMultileCharsetsInMetaTags">shouldSelectFirstCharsetOnWeirdMultileCharsetsInMetaTags</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="shouldReturnNullForIllegalCharsetNames"></a><a href="org/jsoup/helper/0_DataUtilTest.html#shouldReturnNullForIllegalCharsetNames">shouldReturnNullForIllegalCharsetNames</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/0_DataUtilTest.html">DataUtilTest</a></td><td><a name="supportsXmlCharsetDeclaration"></a><a href="org/jsoup/helper/0_DataUtilTest.html#supportsXmlCharsetDeclaration">supportsXmlCharsetDeclaration</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="throwsExceptionOnParseWithoutExecute"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#throwsExceptionOnParseWithoutExecute">throwsExceptionOnParseWithoutExecute</a></td><td>Success</td><td></td><td>0.009</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="connectWithUrl"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#connectWithUrl">connectWithUrl</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="ignoresEmptySetCookies"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#ignoresEmptySetCookies">ignoresEmptySetCookies</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="throwsOnMalformedUrl"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#throwsOnMalformedUrl">throwsOnMalformedUrl</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="multipleHeaders"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#multipleHeaders">multipleHeaders</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="cookie"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#cookie">cookie</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="timeout"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#timeout">timeout</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="method"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#method">method</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="handlesHeaderEncodingOnRequest"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#handlesHeaderEncodingOnRequest">handlesHeaderEncodingOnRequest</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="referrer"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#referrer">referrer</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="sameHeadersCombineWithComma"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#sameHeadersCombineWithComma">sameHeadersCombineWithComma</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="inputStream"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#inputStream">inputStream</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="throwsExceptionOnBodyAsBytesWithoutExecute"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#throwsExceptionOnBodyAsBytesWithoutExecute">throwsExceptionOnBodyAsBytesWithoutExecute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="data"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#data">data</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="userAgent"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#userAgent">userAgent</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="throwsExceptionOnBodyWithoutExecute"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#throwsExceptionOnBodyWithoutExecute">throwsExceptionOnBodyWithoutExecute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="headers"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#headers">headers</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="noUrlThrowsValidationError"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#noUrlThrowsValidationError">noUrlThrowsValidationError</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="requestBody"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#requestBody">requestBody</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="ignoresEmptyCookieNameAndVals"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#ignoresEmptyCookieNameAndVals">ignoresEmptyCookieNameAndVals</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="encodeUrl"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#encodeUrl">encodeUrl</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="throwsOnOddData"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#throwsOnOddData">throwsOnOddData</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/1_HttpConnectionTest.html">HttpConnectionTest</a></td><td><a name="caseInsensitiveHeaders"></a><a href="org/jsoup/helper/1_HttpConnectionTest.html#caseInsensitiveHeaders">caseInsensitiveHeaders</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/2_W3CDomTest.html">W3CDomTest</a></td><td><a name="simpleConversion"></a><a href="org/jsoup/helper/2_W3CDomTest.html#simpleConversion">simpleConversion</a></td><td>Success</td><td></td><td>0.059</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/2_W3CDomTest.html">W3CDomTest</a></td><td><a name="namespacePreservation"></a><a href="org/jsoup/helper/2_W3CDomTest.html#namespacePreservation">namespacePreservation</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/2_W3CDomTest.html">W3CDomTest</a></td><td><a name="convertsGoogleLocation"></a><a href="org/jsoup/helper/2_W3CDomTest.html#convertsGoogleLocation">convertsGoogleLocation</a></td><td>Success</td><td></td><td>0.068</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/2_W3CDomTest.html">W3CDomTest</a></td><td><a name="treatsUndeclaredNamespaceAsLocalName"></a><a href="org/jsoup/helper/2_W3CDomTest.html#treatsUndeclaredNamespaceAsLocalName">treatsUndeclaredNamespaceAsLocalName</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/2_W3CDomTest.html">W3CDomTest</a></td><td><a name="handlesInvalidAttributeNames"></a><a href="org/jsoup/helper/2_W3CDomTest.html#handlesInvalidAttributeNames">handlesInvalidAttributeNames</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/helper/2_W3CDomTest.html">W3CDomTest</a></td><td><a name="convertsGoogle"></a><a href="org/jsoup/helper/2_W3CDomTest.html#convertsGoogle">convertsGoogle</a></td><td>Success</td><td></td><td>0.032</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testYahooJp"></a><a href="org/jsoup/integration/3_ParseTest.html#testYahooJp">testYahooJp</a></td><td>Success</td><td></td><td>0.049</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testBaiduVariant"></a><a href="org/jsoup/integration/3_ParseTest.html#testBaiduVariant">testBaiduVariant</a></td><td>Success</td><td></td><td>0.009</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testGoogleSearchIpod"></a><a href="org/jsoup/integration/3_ParseTest.html#testGoogleSearchIpod">testGoogleSearchIpod</a></td><td>Success</td><td></td><td>0.024</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testBaidu"></a><a href="org/jsoup/integration/3_ParseTest.html#testBaidu">testBaidu</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testBinaryThrowsException"></a><a href="org/jsoup/integration/3_ParseTest.html#testBinaryThrowsException">testBinaryThrowsException</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testSmhBizArticle"></a><a href="org/jsoup/integration/3_ParseTest.html#testSmhBizArticle">testSmhBizArticle</a></td><td>Success</td><td></td><td>0.008</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testYahooArticle"></a><a href="org/jsoup/integration/3_ParseTest.html#testYahooArticle">testYahooArticle</a></td><td>Success</td><td></td><td>0.008</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testBrokenHtml5CharsetWithASingleDoubleQuote"></a><a href="org/jsoup/integration/3_ParseTest.html#testBrokenHtml5CharsetWithASingleDoubleQuote">testBrokenHtml5CharsetWithASingleDoubleQuote</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testNytArticle"></a><a href="org/jsoup/integration/3_ParseTest.html#testNytArticle">testNytArticle</a></td><td>Success</td><td></td><td>0.006</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testLowercaseUtf8Charset"></a><a href="org/jsoup/integration/3_ParseTest.html#testLowercaseUtf8Charset">testLowercaseUtf8Charset</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testNewsHomepage"></a><a href="org/jsoup/integration/3_ParseTest.html#testNewsHomepage">testNewsHomepage</a></td><td>Success</td><td></td><td>0.011</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/integration/3_ParseTest.html">ParseTest</a></td><td><a name="testHtml5Charset"></a><a href="org/jsoup/integration/3_ParseTest.html#testHtml5Charset">testHtml5Charset</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="isWhitespace"></a><a href="org/jsoup/internal/4_StringUtilTest.html#isWhitespace">isWhitespace</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="padding"></a><a href="org/jsoup/internal/4_StringUtilTest.html#padding">padding</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="join"></a><a href="org/jsoup/internal/4_StringUtilTest.html#join">join</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="paddingInACan"></a><a href="org/jsoup/internal/4_StringUtilTest.html#paddingInACan">paddingInACan</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="normaliseWhiteSpaceHandlesHighSurrogates"></a><a href="org/jsoup/internal/4_StringUtilTest.html#normaliseWhiteSpaceHandlesHighSurrogates">normaliseWhiteSpaceHandlesHighSurrogates</a></td><td>Success</td><td></td><td>0.030</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="isNumeric"></a><a href="org/jsoup/internal/4_StringUtilTest.html#isNumeric">isNumeric</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="resolvesRelativeUrls"></a><a href="org/jsoup/internal/4_StringUtilTest.html#resolvesRelativeUrls">resolvesRelativeUrls</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="normaliseWhiteSpace"></a><a href="org/jsoup/internal/4_StringUtilTest.html#normaliseWhiteSpace">normaliseWhiteSpace</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/internal/4_StringUtilTest.html">StringUtilTest</a></td><td><a name="isBlank"></a><a href="org/jsoup/internal/4_StringUtilTest.html#isBlank">isBlank</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/5_AttributeTest.html">AttributeTest</a></td><td><a name="settersOnOrphanAttribute"></a><a href="org/jsoup/nodes/5_AttributeTest.html#settersOnOrphanAttribute">settersOnOrphanAttribute</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/5_AttributeTest.html">AttributeTest</a></td><td><a name="testWithSupplementaryCharacterInAttributeKeyAndValue"></a><a href="org/jsoup/nodes/5_AttributeTest.html#testWithSupplementaryCharacterInAttributeKeyAndValue">testWithSupplementaryCharacterInAttributeKeyAndValue</a></td><td>Success</td><td></td><td>0.013</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/5_AttributeTest.html">AttributeTest</a></td><td><a name="html"></a><a href="org/jsoup/nodes/5_AttributeTest.html#html">html</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/5_AttributeTest.html">AttributeTest</a></td><td><a name="booleanAttributesAreEmptyStringValues"></a><a href="org/jsoup/nodes/5_AttributeTest.html#booleanAttributesAreEmptyStringValues">booleanAttributesAreEmptyStringValues</a></td><td>Success</td><td></td><td>0.010</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/5_AttributeTest.html">AttributeTest</a></td><td><a name="validatesKeysNotEmpty"></a><a href="org/jsoup/nodes/5_AttributeTest.html#validatesKeysNotEmpty">validatesKeysNotEmpty</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/5_AttributeTest.html">AttributeTest</a></td><td><a name="validatesKeysNotEmptyViaSet"></a><a href="org/jsoup/nodes/5_AttributeTest.html#validatesKeysNotEmptyViaSet">validatesKeysNotEmptyViaSet</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="testIteratorRemovable"></a><a href="org/jsoup/nodes/6_AttributesTest.html#testIteratorRemovable">testIteratorRemovable</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="testIteratorHasNext"></a><a href="org/jsoup/nodes/6_AttributesTest.html#testIteratorHasNext">testIteratorHasNext</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="removeCaseSensitive"></a><a href="org/jsoup/nodes/6_AttributesTest.html#removeCaseSensitive">removeCaseSensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="html"></a><a href="org/jsoup/nodes/6_AttributesTest.html#html">html</a></td><td>Success</td><td></td><td>0.012</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="testSetKeyConsistency"></a><a href="org/jsoup/nodes/6_AttributesTest.html#testSetKeyConsistency">testSetKeyConsistency</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="testIteratorEmpty"></a><a href="org/jsoup/nodes/6_AttributesTest.html#testIteratorEmpty">testIteratorEmpty</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="testIterator"></a><a href="org/jsoup/nodes/6_AttributesTest.html#testIterator">testIterator</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/6_AttributesTest.html">AttributesTest</a></td><td><a name="testIteratorUpdateable"></a><a href="org/jsoup/nodes/6_AttributesTest.html#testIteratorUpdateable">testIteratorUpdateable</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testOutputEncoding"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testOutputEncoding">testOutputEncoding</a></td><td>Success</td><td></td><td>0.026</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateDisabledNoChanges"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateDisabledNoChanges">testMetaCharsetUpdateDisabledNoChanges</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testHtmlAppendable"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testHtmlAppendable">testHtmlAppendable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testLocation"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testLocation">testLocation</a></td><td>Success</td><td></td><td>0.046</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testClonesDeclarations"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testClonesDeclarations">testClonesDeclarations</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testClone"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testClone">testClone</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testShiftJisRoundtrip"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testShiftJisRoundtrip">testShiftJisRoundtrip</a></td><td>Success</td><td></td><td>0.006</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testNormalisesStructure"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testNormalisesStructure">testNormalisesStructure</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testXhtmlReferences"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testXhtmlReferences">testXhtmlReferences</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateXmlNoCharset"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateXmlNoCharset">testMetaCharsetUpdateXmlNoCharset</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateUtf8"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateUtf8">testMetaCharsetUpdateUtf8</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateEnabledAfterCharsetChange"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateEnabledAfterCharsetChange">testMetaCharsetUpdateEnabledAfterCharsetChange</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateDisabled"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateDisabled">testMetaCharsetUpdateDisabled</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateXmlDisabled"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateXmlDisabled">testMetaCharsetUpdateXmlDisabled</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateCleanup"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateCleanup">testMetaCharsetUpdateCleanup</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="DocumentsWithSameContentAreEqual"></a><a href="org/jsoup/nodes/7_DocumentTest.html#DocumentsWithSameContentAreEqual">DocumentsWithSameContentAreEqual</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdatedDisabledPerDefault"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdatedDisabledPerDefault">testMetaCharsetUpdatedDisabledPerDefault</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateXmlIso8859"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateXmlIso8859">testMetaCharsetUpdateXmlIso8859</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="parseAndHtmlOnDifferentThreads"></a><a href="org/jsoup/nodes/7_DocumentTest.html#parseAndHtmlOnDifferentThreads">parseAndHtmlOnDifferentThreads</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateNoCharset"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateNoCharset">testMetaCharsetUpdateNoCharset</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="DocumentsWithSameContentAreVerifialbe"></a><a href="org/jsoup/nodes/7_DocumentTest.html#DocumentsWithSameContentAreVerifialbe">DocumentsWithSameContentAreVerifialbe</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="setTextPreservesDocumentStructure"></a><a href="org/jsoup/nodes/7_DocumentTest.html#setTextPreservesDocumentStructure">setTextPreservesDocumentStructure</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="htmlParseDefaultsToHtmlOutputSyntax"></a><a href="org/jsoup/nodes/7_DocumentTest.html#htmlParseDefaultsToHtmlOutputSyntax">htmlParseDefaultsToHtmlOutputSyntax</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateIso8859"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateIso8859">testMetaCharsetUpdateIso8859</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateXmlUtf8"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateXmlUtf8">testMetaCharsetUpdateXmlUtf8</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testHtmlAndXmlSyntax"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testHtmlAndXmlSyntax">testHtmlAndXmlSyntax</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testTitles"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testTitles">testTitles</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/7_DocumentTest.html">DocumentTest</a></td><td><a name="testMetaCharsetUpdateXmlDisabledNoChanges"></a><a href="org/jsoup/nodes/7_DocumentTest.html#testMetaCharsetUpdateXmlDisabledNoChanges">testMetaCharsetUpdateXmlDisabledNoChanges</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/8_DocumentTypeTest.html">DocumentTypeTest</a></td><td><a name="outerHtmlGeneration"></a><a href="org/jsoup/nodes/8_DocumentTypeTest.html#outerHtmlGeneration">outerHtmlGeneration</a></td><td>Success</td><td></td><td>0.018</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/8_DocumentTypeTest.html">DocumentTypeTest</a></td><td><a name="constructorValidationOkWithBlankName"></a><a href="org/jsoup/nodes/8_DocumentTypeTest.html#constructorValidationOkWithBlankName">constructorValidationOkWithBlankName</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/8_DocumentTypeTest.html">DocumentTypeTest</a></td><td><a name="constructorValidationOkWithBlankPublicAndSystemIds"></a><a href="org/jsoup/nodes/8_DocumentTypeTest.html#constructorValidationOkWithBlankPublicAndSystemIds">constructorValidationOkWithBlankPublicAndSystemIds</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/8_DocumentTypeTest.html">DocumentTypeTest</a></td><td><a name="testRoundTrip"></a><a href="org/jsoup/nodes/8_DocumentTypeTest.html#testRoundTrip">testRoundTrip</a></td><td>Success</td><td></td><td>0.009</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/8_DocumentTypeTest.html">DocumentTypeTest</a></td><td><a name="constructorValidationThrowsExceptionOnNulls"></a><a href="org/jsoup/nodes/8_DocumentTypeTest.html#constructorValidationThrowsExceptionOnNulls">constructorValidationThrowsExceptionOnNulls</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testFormatOutline"></a><a href="org/jsoup/nodes/9_ElementTest.html#testFormatOutline">testFormatOutline</a></td><td>Success</td><td></td><td>0.027</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="parentlessToString"></a><a href="org/jsoup/nodes/9_ElementTest.html#parentlessToString">parentlessToString</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="insertChildrenArgumentValidation"></a><a href="org/jsoup/nodes/9_ElementTest.html#insertChildrenArgumentValidation">insertChildrenArgumentValidation</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testPrependElement"></a><a href="org/jsoup/nodes/9_ElementTest.html#testPrependElement">testPrependElement</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testOuterHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testOuterHtml">testOuterHtml</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testHashcodeIsStableWithContentChanges"></a><a href="org/jsoup/nodes/9_ElementTest.html#testHashcodeIsStableWithContentChanges">testHashcodeIsStableWithContentChanges</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="voidTestFilterCallReturnsElement"></a><a href="org/jsoup/nodes/9_ElementTest.html#voidTestFilterCallReturnsElement">voidTestFilterCallReturnsElement</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="elementByTagName"></a><a href="org/jsoup/nodes/9_ElementTest.html#elementByTagName">elementByTagName</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testClonesClassnames"></a><a href="org/jsoup/nodes/9_ElementTest.html#testClonesClassnames">testClonesClassnames</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="appendMustCorrectlyMoveChildrenInsideOneParentElement"></a><a href="org/jsoup/nodes/9_ElementTest.html#appendMustCorrectlyMoveChildrenInsideOneParentElement">appendMustCorrectlyMoveChildrenInsideOneParentElement</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="before"></a><a href="org/jsoup/nodes/9_ElementTest.html#before">before</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testRemoveBeforeIndex"></a><a href="org/jsoup/nodes/9_ElementTest.html#testRemoveBeforeIndex">testRemoveBeforeIndex</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testChainedRemoveAttributes"></a><a href="org/jsoup/nodes/9_ElementTest.html#testChainedRemoveAttributes">testChainedRemoveAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="booleanAttributeOutput"></a><a href="org/jsoup/nodes/9_ElementTest.html#booleanAttributeOutput">booleanAttributeOutput</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testAddNewHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testAddNewHtml">testAddNewHtml</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testAddNewText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testAddNewText">testAddNewText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testPreviousElementSiblings"></a><a href="org/jsoup/nodes/9_ElementTest.html#testPreviousElementSiblings">testPreviousElementSiblings</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetElementById"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetElementById">testGetElementById</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testClone"></a><a href="org/jsoup/nodes/9_ElementTest.html#testClone">testClone</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testHtmlContainsOuter"></a><a href="org/jsoup/nodes/9_ElementTest.html#testHtmlContainsOuter">testHtmlContainsOuter</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testClassDomMethods"></a><a href="org/jsoup/nodes/9_ElementTest.html#testClassDomMethods">testClassDomMethods</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testRoot"></a><a href="org/jsoup/nodes/9_ElementTest.html#testRoot">testRoot</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testWrap"></a><a href="org/jsoup/nodes/9_ElementTest.html#testWrap">testWrap</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNamespacedElements"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNamespacedElements">testNamespacedElements</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testClassNames"></a><a href="org/jsoup/nodes/9_ElementTest.html#testClassNames">testClassNames</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testElementSiblingIndex"></a><a href="org/jsoup/nodes/9_ElementTest.html#testElementSiblingIndex">testElementSiblingIndex</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testIs"></a><a href="org/jsoup/nodes/9_ElementTest.html#testIs">testIs</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testEmptyElementFormatHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testEmptyElementFormatHtml">testEmptyElementFormatHtml</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testLoopedRemoveAttributes"></a><a href="org/jsoup/nodes/9_ElementTest.html#testLoopedRemoveAttributes">testLoopedRemoveAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testShadowElementsAreUpdated"></a><a href="org/jsoup/nodes/9_ElementTest.html#testShadowElementsAreUpdated">testShadowElementsAreUpdated</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testKeepsPreTextAtDepth"></a><a href="org/jsoup/nodes/9_ElementTest.html#testKeepsPreTextAtDepth">testKeepsPreTextAtDepth</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetElementsWithAttribute"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetElementsWithAttribute">testGetElementsWithAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNoIndentOnScriptAndStyle"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNoIndentOnScriptAndStyle">testNoIndentOnScriptAndStyle</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testAppendRowToTable"></a><a href="org/jsoup/nodes/9_ElementTest.html#testAppendRowToTable">testAppendRowToTable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetElementsWithClass"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetElementsWithClass">testGetElementsWithClass</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testShallowClone"></a><a href="org/jsoup/nodes/9_ElementTest.html#testShallowClone">testShallowClone</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testWrapWithRemainder"></a><a href="org/jsoup/nodes/9_ElementTest.html#testWrapWithRemainder">testWrapWithRemainder</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testClearAttributes"></a><a href="org/jsoup/nodes/9_ElementTest.html#testClearAttributes">testClearAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testKeepsPreTextInCode"></a><a href="org/jsoup/nodes/9_ElementTest.html#testKeepsPreTextInCode">testKeepsPreTextInCode</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testHasClassDomMethods"></a><a href="org/jsoup/nodes/9_ElementTest.html#testHasClassDomMethods">testHasClassDomMethods</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetParents"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetParents">testGetParents</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetText">testGetText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="getElementsByTagName"></a><a href="org/jsoup/nodes/9_ElementTest.html#getElementsByTagName">getElementsByTagName</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testFormatHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testFormatHtml">testFormatHtml</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetElementsWithAttributeValue"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetElementsWithAttributeValue">testGetElementsWithAttributeValue</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testWholeText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testWholeText">testWholeText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetElementsWithAttributeDash"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetElementsWithAttributeDash">testGetElementsWithAttributeDash</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testManipulateTextNodes"></a><a href="org/jsoup/nodes/9_ElementTest.html#testManipulateTextNodes">testManipulateTextNodes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testChildThrowsIndexOutOfBoundsOnMissing"></a><a href="org/jsoup/nodes/9_ElementTest.html#testChildThrowsIndexOutOfBoundsOnMissing">testChildThrowsIndexOutOfBoundsOnMissing</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testPrependRowToTable"></a><a href="org/jsoup/nodes/9_ElementTest.html#testPrependRowToTable">testPrependRowToTable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetTextNodes"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetTextNodes">testGetTextNodes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="after"></a><a href="org/jsoup/nodes/9_ElementTest.html#after">after</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testContainerOutput"></a><a href="org/jsoup/nodes/9_ElementTest.html#testContainerOutput">testContainerOutput</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testInnerHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testInnerHtml">testInnerHtml</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testHasText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testHasText">testHasText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testRemoveAttr"></a><a href="org/jsoup/nodes/9_ElementTest.html#testRemoveAttr">testRemoveAttr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="textHasSpaceBetweenDivAndCenterTags"></a><a href="org/jsoup/nodes/9_ElementTest.html#textHasSpaceBetweenDivAndCenterTags">textHasSpaceBetweenDivAndCenterTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetDataNodes"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetDataNodes">testGetDataNodes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testCssPath"></a><a href="org/jsoup/nodes/9_ElementTest.html#testCssPath">testCssPath</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testSetHtmlTitle"></a><a href="org/jsoup/nodes/9_ElementTest.html#testSetHtmlTitle">testSetHtmlTitle</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testChildrenElements"></a><a href="org/jsoup/nodes/9_ElementTest.html#testChildrenElements">testChildrenElements</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="moveByAppend"></a><a href="org/jsoup/nodes/9_ElementTest.html#moveByAppend">moveByAppend</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNormalisesText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNormalisesText">testNormalisesText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testClassUpdates"></a><a href="org/jsoup/nodes/9_ElementTest.html#testClassUpdates">testClassUpdates</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testAddNewElement"></a><a href="org/jsoup/nodes/9_ElementTest.html#testAddNewElement">testAddNewElement</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testHashAndEqualsAndValue"></a><a href="org/jsoup/nodes/9_ElementTest.html#testHashAndEqualsAndValue">testHashAndEqualsAndValue</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testElementSiblingIndexSameContent"></a><a href="org/jsoup/nodes/9_ElementTest.html#testElementSiblingIndexSameContent">testElementSiblingIndexSameContent</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetChildText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetChildText">testGetChildText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testRelativeUrls"></a><a href="org/jsoup/nodes/9_ElementTest.html#testRelativeUrls">testRelativeUrls</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testSetIndent"></a><a href="org/jsoup/nodes/9_ElementTest.html#testSetIndent">testSetIndent</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testThrowsOnAddNullText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testThrowsOnAddNullText">testThrowsOnAddNullText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetSiblingsWithDuplicateContent"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetSiblingsWithDuplicateContent">testGetSiblingsWithDuplicateContent</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNormalizesInvisiblesInText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNormalizesInvisiblesInText">testNormalizesInvisiblesInText</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="insertChildrenAsCopy"></a><a href="org/jsoup/nodes/9_ElementTest.html#insertChildrenAsCopy">insertChildrenAsCopy</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testTagNameSet"></a><a href="org/jsoup/nodes/9_ElementTest.html#testTagNameSet">testTagNameSet</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="dataset"></a><a href="org/jsoup/nodes/9_ElementTest.html#dataset">dataset</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testPrependText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testPrependText">testPrependText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNormalizesNbspInText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNormalizesNbspInText">testNormalizesNbspInText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testRemoveAfterIndex"></a><a href="org/jsoup/nodes/9_ElementTest.html#testRemoveAfterIndex">testRemoveAfterIndex</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testThrowsOnPrependNullText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testThrowsOnPrependNullText">testThrowsOnPrependNullText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testGetSiblings"></a><a href="org/jsoup/nodes/9_ElementTest.html#testGetSiblings">testGetSiblings</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testSetHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testSetHtml">testSetHtml</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testSetText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testSetText">testSetText</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNextElementSiblings"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNextElementSiblings">testNextElementSiblings</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testAddBooleanAttribute"></a><a href="org/jsoup/nodes/9_ElementTest.html#testAddBooleanAttribute">testAddBooleanAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="textHasSpaceAfterBlockTags"></a><a href="org/jsoup/nodes/9_ElementTest.html#textHasSpaceAfterBlockTags">textHasSpaceAfterBlockTags</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="whiteSpaceClassElement"></a><a href="org/jsoup/nodes/9_ElementTest.html#whiteSpaceClassElement">whiteSpaceClassElement</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="classNamesAndAttributeNameIsCaseInsensitive"></a><a href="org/jsoup/nodes/9_ElementTest.html#classNamesAndAttributeNameIsCaseInsensitive">classNamesAndAttributeNameIsCaseInsensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testPrependNewHtml"></a><a href="org/jsoup/nodes/9_ElementTest.html#testPrependNewHtml">testPrependNewHtml</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testRemovingEmptyClassAttributeWhenLastClassRemoved"></a><a href="org/jsoup/nodes/9_ElementTest.html#testRemovingEmptyClassAttributeWhenLastClassRemoved">testRemovingEmptyClassAttributeWhenLastClassRemoved</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testBrHasSpace"></a><a href="org/jsoup/nodes/9_ElementTest.html#testBrHasSpace">testBrHasSpace</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testKeepsPreText"></a><a href="org/jsoup/nodes/9_ElementTest.html#testKeepsPreText">testKeepsPreText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNotPretty"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNotPretty">testNotPretty</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testNextElementSiblingAfterClone"></a><a href="org/jsoup/nodes/9_ElementTest.html#testNextElementSiblingAfterClone">testNextElementSiblingAfterClone</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testAppendTo"></a><a href="org/jsoup/nodes/9_ElementTest.html#testAppendTo">testAppendTo</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="getNamespacedElementsByTag"></a><a href="org/jsoup/nodes/9_ElementTest.html#getNamespacedElementsByTag">getNamespacedElementsByTag</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="testTraverse"></a><a href="org/jsoup/nodes/9_ElementTest.html#testTraverse">testTraverse</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="insertChildrenAtPosition"></a><a href="org/jsoup/nodes/9_ElementTest.html#insertChildrenAtPosition">insertChildrenAtPosition</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/9_ElementTest.html">ElementTest</a></td><td><a name="elementIsNotASiblingOfItself"></a><a href="org/jsoup/nodes/9_ElementTest.html#elementIsNotASiblingOfItself">elementIsNotASiblingOfItself</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="strictUnescape"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#strictUnescape">strictUnescape</a></td><td>Success</td><td></td><td>0.013</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="escapeSupplementaryCharacter"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#escapeSupplementaryCharacter">escapeSupplementaryCharacter</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="escapesGtInXmlAttributesButNotInHtml"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#escapesGtInXmlAttributesButNotInHtml">escapesGtInXmlAttributesButNotInHtml</a></td><td>Success</td><td></td><td>0.010</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="escape"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#escape">escape</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="unescape"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#unescape">unescape</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="caseSensitive"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#caseSensitive">caseSensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="noSpuriousDecodes"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#noSpuriousDecodes">noSpuriousDecodes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="notMissingMultis"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#notMissingMultis">notMissingMultis</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="xhtml"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#xhtml">xhtml</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="getByName"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#getByName">getByName</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="notMissingSupplementals"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#notMissingSupplementals">notMissingSupplementals</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="quoteReplacements"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#quoteReplacements">quoteReplacements</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="escapedSupplementary"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#escapedSupplementary">escapedSupplementary</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="letterDigitEntities"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#letterDigitEntities">letterDigitEntities</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/10_EntitiesTest.html">EntitiesTest</a></td><td><a name="unescapeMultiChars"></a><a href="org/jsoup/nodes/10_EntitiesTest.html#unescapeMultiChars">unescapeMultiChars</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="createsFormData"></a><a href="org/jsoup/nodes/11_FormElementTest.html#createsFormData">createsFormData</a></td><td>Success</td><td></td><td>0.027</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="actionWithNoValue"></a><a href="org/jsoup/nodes/11_FormElementTest.html#actionWithNoValue">actionWithNoValue</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="removeFormElement"></a><a href="org/jsoup/nodes/11_FormElementTest.html#removeFormElement">removeFormElement</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="formsAddedAfterParseAreFormElements"></a><a href="org/jsoup/nodes/11_FormElementTest.html#formsAddedAfterParseAreFormElements">formsAddedAfterParseAreFormElements</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="createsSubmitableConnection"></a><a href="org/jsoup/nodes/11_FormElementTest.html#createsSubmitableConnection">createsSubmitableConnection</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="formDataUsesFirstAttribute"></a><a href="org/jsoup/nodes/11_FormElementTest.html#formDataUsesFirstAttribute">formDataUsesFirstAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="controlsAddedAfterParseAreLinkedWithForms"></a><a href="org/jsoup/nodes/11_FormElementTest.html#controlsAddedAfterParseAreLinkedWithForms">controlsAddedAfterParseAreLinkedWithForms</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="adoptedFormsRetainInputs"></a><a href="org/jsoup/nodes/11_FormElementTest.html#adoptedFormsRetainInputs">adoptedFormsRetainInputs</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="usesOnForCheckboxValueIfNoValueSet"></a><a href="org/jsoup/nodes/11_FormElementTest.html#usesOnForCheckboxValueIfNoValueSet">usesOnForCheckboxValueIfNoValueSet</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="hasAssociatedControls"></a><a href="org/jsoup/nodes/11_FormElementTest.html#hasAssociatedControls">hasAssociatedControls</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/11_FormElementTest.html">FormElementTest</a></td><td><a name="actionWithNoBaseUri"></a><a href="org/jsoup/nodes/11_FormElementTest.html#actionWithNoBaseUri">actionWithNoBaseUri</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handlesAbsOnProtocolessAbsoluteUris"></a><a href="org/jsoup/nodes/12_NodeTest.html#handlesAbsOnProtocolessAbsoluteUris">handlesAbsOnProtocolessAbsoluteUris</a></td><td>Success</td><td></td><td>0.026</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="setBaseUriIsRecursive"></a><a href="org/jsoup/nodes/12_NodeTest.html#setBaseUriIsRecursive">setBaseUriIsRecursive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="ownerDocument"></a><a href="org/jsoup/nodes/12_NodeTest.html#ownerDocument">ownerDocument</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="before"></a><a href="org/jsoup/nodes/12_NodeTest.html#before">before</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handleAbsOnLocalhostFileUris"></a><a href="org/jsoup/nodes/12_NodeTest.html#handleAbsOnLocalhostFileUris">handleAbsOnLocalhostFileUris</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="unwrap"></a><a href="org/jsoup/nodes/12_NodeTest.html#unwrap">unwrap</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="absHandlesDotFromIndex"></a><a href="org/jsoup/nodes/12_NodeTest.html#absHandlesDotFromIndex">absHandlesDotFromIndex</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="supportsClone"></a><a href="org/jsoup/nodes/12_NodeTest.html#supportsClone">supportsClone</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="root"></a><a href="org/jsoup/nodes/12_NodeTest.html#root">root</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="after"></a><a href="org/jsoup/nodes/12_NodeTest.html#after">after</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="absHandlesRelativeQuery"></a><a href="org/jsoup/nodes/12_NodeTest.html#absHandlesRelativeQuery">absHandlesRelativeQuery</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="unwrapNoChildren"></a><a href="org/jsoup/nodes/12_NodeTest.html#unwrapNoChildren">unwrapNoChildren</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="literalAbsPrefix"></a><a href="org/jsoup/nodes/12_NodeTest.html#literalAbsPrefix">literalAbsPrefix</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handlesBaseUri"></a><a href="org/jsoup/nodes/12_NodeTest.html#handlesBaseUri">handlesBaseUri</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handlesAbsOnImage"></a><a href="org/jsoup/nodes/12_NodeTest.html#handlesAbsOnImage">handlesAbsOnImage</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="nodeIsNotASiblingOfItself"></a><a href="org/jsoup/nodes/12_NodeTest.html#nodeIsNotASiblingOfItself">nodeIsNotASiblingOfItself</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handleAbsOnFileUris"></a><a href="org/jsoup/nodes/12_NodeTest.html#handleAbsOnFileUris">handleAbsOnFileUris</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="testReplace"></a><a href="org/jsoup/nodes/12_NodeTest.html#testReplace">testReplace</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handlesAbsPrefixOnHasAttr"></a><a href="org/jsoup/nodes/12_NodeTest.html#handlesAbsPrefixOnHasAttr">handlesAbsPrefixOnHasAttr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="childNodesCopy"></a><a href="org/jsoup/nodes/12_NodeTest.html#childNodesCopy">childNodesCopy</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="changingAttributeValueShouldReplaceExistingAttributeCaseInsensitive"></a><a href="org/jsoup/nodes/12_NodeTest.html#changingAttributeValueShouldReplaceExistingAttributeCaseInsensitive">changingAttributeValueShouldReplaceExistingAttributeCaseInsensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="traverse"></a><a href="org/jsoup/nodes/12_NodeTest.html#traverse">traverse</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="orphanNodeReturnsNullForSiblingElements"></a><a href="org/jsoup/nodes/12_NodeTest.html#orphanNodeReturnsNullForSiblingElements">orphanNodeReturnsNullForSiblingElements</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="handlesAbsPrefix"></a><a href="org/jsoup/nodes/12_NodeTest.html#handlesAbsPrefix">handlesAbsPrefix</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/12_NodeTest.html">NodeTest</a></td><td><a name="testRemove"></a><a href="org/jsoup/nodes/12_NodeTest.html#testRemove">testRemove</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/13_TextNodeTest.html">TextNodeTest</a></td><td><a name="testWithSupplementaryCharacter"></a><a href="org/jsoup/nodes/13_TextNodeTest.html#testWithSupplementaryCharacter">testWithSupplementaryCharacter</a></td><td>Success</td><td></td><td>0.040</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/13_TextNodeTest.html">TextNodeTest</a></td><td><a name="testLeadNodesHaveNoChildren"></a><a href="org/jsoup/nodes/13_TextNodeTest.html#testLeadNodesHaveNoChildren">testLeadNodesHaveNoChildren</a></td><td>Success</td><td></td><td>0.004</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/13_TextNodeTest.html">TextNodeTest</a></td><td><a name="testBlank"></a><a href="org/jsoup/nodes/13_TextNodeTest.html#testBlank">testBlank</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/13_TextNodeTest.html">TextNodeTest</a></td><td><a name="testTextBean"></a><a href="org/jsoup/nodes/13_TextNodeTest.html#testTextBean">testTextBean</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/13_TextNodeTest.html">TextNodeTest</a></td><td><a name="testSplitText"></a><a href="org/jsoup/nodes/13_TextNodeTest.html#testSplitText">testSplitText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/nodes/13_TextNodeTest.html">TextNodeTest</a></td><td><a name="testSplitAnEmbolden"></a><a href="org/jsoup/nodes/13_TextNodeTest.html#testSplitAnEmbolden">testSplitAnEmbolden</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="dropsSlashFromAttributeName"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#dropsSlashFromAttributeName">dropsSlashFromAttributeName</a></td><td>Success</td><td></td><td>0.029</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="moreAttributeUnescapes"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#moreAttributeUnescapes">moreAttributeUnescapes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="parsesBooleanAttributes"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#parsesBooleanAttributes">parsesBooleanAttributes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="handlesNewLinesAndReturns"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#handlesNewLinesAndReturns">handlesNewLinesAndReturns</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="canStartWithEq"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#canStartWithEq">canStartWithEq</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="parsesRoughAttributeString"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#parsesRoughAttributeString">parsesRoughAttributeString</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="strictAttributeUnescapes"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#strictAttributeUnescapes">strictAttributeUnescapes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/14_AttributeParseTest.html">AttributeParseTest</a></td><td><a name="parsesEmptyString"></a><a href="org/jsoup/parser/14_AttributeParseTest.html#parsesEmptyString">parsesEmptyString</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="nextIndexOfUnmatched"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#nextIndexOfUnmatched">nextIndexOfUnmatched</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="unconsume"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#unconsume">unconsume</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="bufferUp"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#bufferUp">bufferUp</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="nextIndexOfChar"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#nextIndexOfChar">nextIndexOfChar</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="notEmptyAtBufferSplitPoint"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#notEmptyAtBufferSplitPoint">notEmptyAtBufferSplitPoint</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="rangeEquals"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#rangeEquals">rangeEquals</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeToChar"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeToChar">consumeToChar</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="advance"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#advance">advance</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeToString"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeToString">consumeToString</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeToAny"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeToAny">consumeToAny</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeToEnd"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeToEnd">consumeToEnd</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeToNonexistentEndWhenAtAnd"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeToNonexistentEndWhenAtAnd">consumeToNonexistentEndWhenAtAnd</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="containsIgnoreCase"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#containsIgnoreCase">containsIgnoreCase</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="mark"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#mark">mark</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="empty"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#empty">empty</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="cachesStrings"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#cachesStrings">cachesStrings</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeLetterThenDigitSequence"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeLetterThenDigitSequence">consumeLetterThenDigitSequence</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consumeLetterSequence"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consumeLetterSequence">consumeLetterSequence</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="nextIndexOfString"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#nextIndexOfString">nextIndexOfString</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="matches"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#matches">matches</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="consume"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#consume">consume</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="matchesIgnoreCase"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#matchesIgnoreCase">matchesIgnoreCase</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a name="matchesAny"></a><a href="org/jsoup/parser/15_CharacterReaderTest.html#matchesAny">matchesAny</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="preservedCaseLinksCantNest"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#preservedCaseLinksCantNest">preservedCaseLinksCantNest</a></td><td>Success</td><td></td><td>0.023</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="normalisesHeadlessBody"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#normalisesHeadlessBody">normalisesHeadlessBody</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesUnterminatedOption"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesUnterminatedOption">parsesUnterminatedOption</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesProtocolRelativeUrl"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesProtocolRelativeUrl">handlesProtocolRelativeUrl</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="dropsUnterminatedTag"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#dropsUnterminatedTag">dropsUnterminatedTag</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="canPreserveAttributeCase"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#canPreserveAttributeCase">canPreserveAttributeCase</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesNullInComments"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesNullInComments">handlesNullInComments</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesTextAfterData"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesTextAfterData">handlesTextAfterData</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="dropsUnterminatedAttribute"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#dropsUnterminatedAttribute">dropsUnterminatedAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testNormalisesIsIndex"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testNormalisesIsIndex">testNormalisesIsIndex</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="noTableDirectInTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#noTableDirectInTable">noTableDirectInTable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testSupportsPartiallyNonAsciiTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testSupportsPartiallyNonAsciiTags">testSupportsPartiallyNonAsciiTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testSpaceAfterTag"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testSpaceAfterTag">testSpaceAfterTag</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesXmlDeclarationAsBogusComment"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesXmlDeclarationAsBogusComment">handlesXmlDeclarationAsBogusComment</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="normalisesDocument"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#normalisesDocument">normalisesDocument</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="createsStructureFromBodySnippet"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#createsStructureFromBodySnippet">createsStructureFromBodySnippet</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="retainsAttributesOfDifferentCaseIfSensitive"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#retainsAttributesOfDifferentCaseIfSensitive">retainsAttributesOfDifferentCaseIfSensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="canPreserveTagCase"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#canPreserveTagCase">canPreserveTagCase</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="preservesSpaceInScript"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#preservesSpaceInScript">preservesSpaceInScript</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesXmlDeclAndCommentsBeforeDoctype"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesXmlDeclAndCommentsBeforeDoctype">handlesXmlDeclAndCommentsBeforeDoctype</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testHtmlLowerCase"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testHtmlLowerCase">testHtmlLowerCase</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesDeepStack"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesDeepStack">handlesDeepStack</a></td><td>Success</td><td></td><td>0.162</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesSimpleDocument"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesSimpleDocument">parsesSimpleDocument</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="doesNotCreateImplicitLists"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#doesNotCreateImplicitLists">doesNotCreateImplicitLists</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesUnterminatedTextarea"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesUnterminatedTextarea">parsesUnterminatedTextarea</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testSupportsNonAsciiTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testSupportsNonAsciiTags">testSupportsNonAsciiTags</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="dropsDuplicateAttributes"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#dropsDuplicateAttributes">dropsDuplicateAttributes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesWhatWgExpensesTableExample"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesWhatWgExpensesTableExample">handlesWhatWgExpensesTableExample</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesImplicitCaptionClose"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesImplicitCaptionClose">handlesImplicitCaptionClose</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesQuotesInCommentsInScripts"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesQuotesInCommentsInScripts">handlesQuotesInCommentsInScripts</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesInputInTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesInputInTable">handlesInputInTable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesInvalidStartTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesInvalidStartTags">handlesInvalidStartTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesEscapedData"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesEscapedData">handlesEscapedData</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="fallbackToUtfIfCantEncode"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#fallbackToUtfIfCantEncode">fallbackToUtfIfCantEncode</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="reconstructFormattingElements"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#reconstructFormattingElements">reconstructFormattingElements</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="createsDocumentStructure"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#createsDocumentStructure">createsDocumentStructure</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesCdataInScript"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesCdataInScript">handlesCdataInScript</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedTitle"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedTitle">handlesUnclosedTitle</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="caseInsensitiveParseTree"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#caseInsensitiveParseTree">caseInsensitiveParseTree</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handleNullContextInParseFragment"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handleNullContextInParseFragment">handleNullContextInParseFragment</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesUnterminatedComments"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesUnterminatedComments">parsesUnterminatedComments</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="emptyTdTag"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#emptyTdTag">emptyTdTag</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="caseSensitiveParseTree"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#caseSensitiveParseTree">caseSensitiveParseTree</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testFragment"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testFragment">testFragment</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedScriptAtEof"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedScriptAtEof">handlesUnclosedScriptAtEof</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesNewlinesAndWhitespaceInTag"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesNewlinesAndWhitespaceInTag">handlesNewlinesAndWhitespaceInTag</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesDataOnlyTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesDataOnlyTags">handlesDataOnlyTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="normalizesDiscordantTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#normalizesDiscordantTags">normalizesDiscordantTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesCdataAcrossBuffer"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesCdataAcrossBuffer">handlesCdataAcrossBuffer</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="noImplicitFormForTextAreas"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#noImplicitFormForTextAreas">noImplicitFormForTextAreas</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="commentBeforeHtml"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#commentBeforeHtml">commentBeforeHtml</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedCdataAtEOF"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedCdataAtEOF">handlesUnclosedCdataAtEOF</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesCdata"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesCdata">handlesCdata</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesKnownEmptyBlocks"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesKnownEmptyBlocks">handlesKnownEmptyBlocks</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="createsFormElements"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#createsFormElements">createsFormElements</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="noErrorsByDefault"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#noErrorsByDefault">noErrorsByDefault</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testHeaderContents"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testHeaderContents">testHeaderContents</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testRelaxedTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testRelaxedTags">testRelaxedTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesKnownEmptyStyle"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesKnownEmptyStyle">handlesKnownEmptyStyle</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesKnownEmptyTitle"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesKnownEmptyTitle">handlesKnownEmptyTitle</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="preservesSpaceInTextArea"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#preservesSpaceInTextArea">preservesSpaceInTextArea</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnexpectedMarkupInTables"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnexpectedMarkupInTables">handlesUnexpectedMarkupInTables</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesKnownEmptyIframe"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesKnownEmptyIframe">handlesKnownEmptyIframe</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="characterReaderBuffer"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#characterReaderBuffer">characterReaderBuffer</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testTemplateInsideTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testTemplateInsideTable">testTemplateInsideTable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="cdataNodesAreTextNodes"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#cdataNodesAreTextNodes">cdataNodesAreTextNodes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="ignoresContentAfterFrameset"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#ignoresContentAfterFrameset">ignoresContentAfterFrameset</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesBaseTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesBaseTags">handlesBaseTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testSpanContents"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testSpanContents">testSpanContents</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handles0CharacterAsText"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handles0CharacterAsText">handles0CharacterAsText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesFrames"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesFrames">handlesFrames</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesJavadocFont"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesJavadocFont">handlesJavadocFont</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testReinsertionModeForThCelss"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testReinsertionModeForThCelss">testReinsertionModeForThCelss</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesControlCodeInAttributeName"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesControlCodeInAttributeName">handlesControlCodeInAttributeName</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesInvalidDoctypes"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesInvalidDoctypes">handlesInvalidDoctypes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesTagsInTextarea"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesTagsInTextarea">handlesTagsInTextarea</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesCommentsInTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesCommentsInTable">handlesCommentsInTable</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesEscapedScript"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesEscapedScript">handlesEscapedScript</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testInvalidTableContents"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testInvalidTableContents">testInvalidTableContents</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnknownTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnknownTags">handlesUnknownTags</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="ignoresDupeEndTrTag"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#ignoresDupeEndTrTag">ignoresDupeEndTrTag</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="associatedFormControlsWithDisjointForms"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#associatedFormControlsWithDisjointForms">associatedFormControlsWithDisjointForms</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesTbodyTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesTbodyTable">handlesTbodyTable</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="normalisedBodyAfterContent"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#normalisedBodyAfterContent">normalisedBodyAfterContent</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesSpanInTbody"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesSpanInTbody">handlesSpanInTbody</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedTitleAtEof"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedTitleAtEof">handlesUnclosedTitleAtEof</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesSolidusAtAttributeEnd"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesSolidusAtAttributeEnd">handlesSolidusAtAttributeEnd</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnknownNamespaceTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnknownNamespaceTags">handlesUnknownNamespaceTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesKnownEmptyNoFrames"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesKnownEmptyNoFrames">handlesKnownEmptyNoFrames</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="roundTripsCdata"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#roundTripsCdata">roundTripsCdata</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesManyChildren"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesManyChildren">handlesManyChildren</a></td><td>Success</td><td></td><td>0.025</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesTextArea"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesTextArea">handlesTextArea</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="convertsImageToImg"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#convertsImageToImg">convertsImageToImg</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="preSkipsFirstNewline"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#preSkipsFirstNewline">preSkipsFirstNewline</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="commentAtEnd"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#commentAtEnd">commentAtEnd</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="selfClosingOnNonvoidIsError"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#selfClosingOnNonvoidIsError">selfClosingOnNonvoidIsError</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesNestedImplicitTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesNestedImplicitTable">handlesNestedImplicitTable</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesRoughAttributes"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesRoughAttributes">parsesRoughAttributes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testUsingSingleQuotesInQueries"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testUsingSingleQuotesInQueries">testUsingSingleQuotesInQueries</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedDefinitionLists"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedDefinitionLists">handlesUnclosedDefinitionLists</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesWhitespaceInoDocType"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesWhitespaceInoDocType">handlesWhitespaceInoDocType</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="reconstructFormattingElementsInTable"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#reconstructFormattingElementsInTable">reconstructFormattingElementsInTable</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="selfClosingVoidIsNotAnError"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#selfClosingVoidIsNotAnError">selfClosingVoidIsNotAnError</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testNoImagesInNoScriptInHead"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testNoImagesInNoScriptInHead">testNoImagesInNoScriptInHead</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesBaseWithoutHref"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesBaseWithoutHref">handlesBaseWithoutHref</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedRawtextAtEof"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedRawtextAtEof">handlesUnclosedRawtextAtEof</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="tracksLimitedErrorsWhenRequested"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#tracksLimitedErrorsWhenRequested">tracksLimitedErrorsWhenRequested</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="doesNotFindShortestMatchingEntity"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#doesNotFindShortestMatchingEntity">doesNotFindShortestMatchingEntity</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="tracksErrorsWhenRequested"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#tracksErrorsWhenRequested">tracksErrorsWhenRequested</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedAnchors"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedAnchors">handlesUnclosedAnchors</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesBodyFragment"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesBodyFragment">parsesBodyFragment</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnknownInlineTags"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnknownInlineTags">handlesUnknownInlineTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testSelectWithOption"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testSelectWithOption">testSelectWithOption</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="normalisesEmptyDocument"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#normalisesEmptyDocument">normalisesEmptyDocument</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="relaxedBaseEntityMatchAndStrictExtendedMatch"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#relaxedBaseEntityMatchAndStrictExtendedMatch">relaxedBaseEntityMatchAndStrictExtendedMatch</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesUnclosedFormattingElements"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesUnclosedFormattingElements">handlesUnclosedFormattingElements</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesSolidusInA"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesSolidusInA">handlesSolidusInA</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testHgroup"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testHgroup">testHgroup</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="discardsNakedTds"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#discardsNakedTds">discardsNakedTds</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesComments"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesComments">parsesComments</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testFontFlowContents"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testFontFlowContents">testFontFlowContents</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesMisnestedTagsBI"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesMisnestedTagsBI">handlesMisnestedTagsBI</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesMisnestedTagsBP"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesMisnestedTagsBP">handlesMisnestedTagsBP</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="canPreserveBothCase"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#canPreserveBothCase">canPreserveBothCase</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testHandlesDeepSpans"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testHandlesDeepSpans">testHandlesDeepSpans</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handleCDataInText"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handleCDataInText">handleCDataInText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="findsCharsetInMalformedMeta"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#findsCharsetInMalformedMeta">findsCharsetInMalformedMeta</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesMultiClosingBody"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesMultiClosingBody">handlesMultiClosingBody</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesBlocksInDefinitions"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesBlocksInDefinitions">handlesBlocksInDefinitions</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="testAFlowContents"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#testAFlowContents">testAFlowContents</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="handlesNullInData"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#handlesNullInData">handlesNullInData</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/16_HtmlParserTest.html">HtmlParserTest</a></td><td><a name="parsesQuiteRoughAttributes"></a><a href="org/jsoup/parser/16_HtmlParserTest.html#parsesQuiteRoughAttributes">parsesQuiteRoughAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/17_HtmlTreeBuilderStateTest.html">HtmlTreeBuilderStateTest</a></td><td><a name="ensureArraysAreSorted"></a><a href="org/jsoup/parser/17_HtmlTreeBuilderStateTest.html#ensureArraysAreSorted">ensureArraysAreSorted</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/18_HtmlTreeBuilderTest.html">HtmlTreeBuilderTest</a></td><td><a name="ensureSearchArraysAreSorted"></a><a href="org/jsoup/parser/18_HtmlTreeBuilderTest.html#ensureSearchArraysAreSorted">ensureSearchArraysAreSorted</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/19_ParserSettingsTest.html">ParserSettingsTest</a></td><td><a name="attributesCaseNormalization"></a><a href="org/jsoup/parser/19_ParserSettingsTest.html#attributesCaseNormalization">attributesCaseNormalization</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/19_ParserSettingsTest.html">ParserSettingsTest</a></td><td><a name="caseSupport"></a><a href="org/jsoup/parser/19_ParserSettingsTest.html#caseSupport">caseSupport</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/19_ParserSettingsTest.html">ParserSettingsTest</a></td><td><a name="attributeCaseNormalization"></a><a href="org/jsoup/parser/19_ParserSettingsTest.html#attributeCaseNormalization">attributeCaseNormalization</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/20_ParserTest.html">ParserTest</a></td><td><a name="unescapeEntitiesHandlesLargeInput"></a><a href="org/jsoup/parser/20_ParserTest.html#unescapeEntitiesHandlesLargeInput">unescapeEntitiesHandlesLargeInput</a></td><td>Success</td><td></td><td>0.011</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/20_ParserTest.html">ParserTest</a></td><td><a name="unescapeEntities"></a><a href="org/jsoup/parser/20_ParserTest.html#unescapeEntities">unescapeEntities</a></td><td>Success</td><td></td><td>0.007</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="divSemantics"></a><a href="org/jsoup/parser/21_TagTest.html#divSemantics">divSemantics</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="trims"></a><a href="org/jsoup/parser/21_TagTest.html#trims">trims</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="imgSemantics"></a><a href="org/jsoup/parser/21_TagTest.html#imgSemantics">imgSemantics</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="valueOfChecksNotEmpty"></a><a href="org/jsoup/parser/21_TagTest.html#valueOfChecksNotEmpty">valueOfChecksNotEmpty</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="pSemantics"></a><a href="org/jsoup/parser/21_TagTest.html#pSemantics">pSemantics</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="equality"></a><a href="org/jsoup/parser/21_TagTest.html#equality">equality</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="defaultSemantics"></a><a href="org/jsoup/parser/21_TagTest.html#defaultSemantics">defaultSemantics</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="canBeInsensitive"></a><a href="org/jsoup/parser/21_TagTest.html#canBeInsensitive">canBeInsensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="valueOfChecksNotNull"></a><a href="org/jsoup/parser/21_TagTest.html#valueOfChecksNotNull">valueOfChecksNotNull</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/21_TagTest.html">TagTest</a></td><td><a name="isCaseSensitive"></a><a href="org/jsoup/parser/21_TagTest.html#isCaseSensitive">isCaseSensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="addFirst"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#addFirst">addFirst</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="unescape"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#unescape">unescape</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="chompToIgnoreCase"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#chompToIgnoreCase">chompToIgnoreCase</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="chompEscapedBalanced"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#chompEscapedBalanced">chompEscapedBalanced</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="consumeToIgnoreSecondCallTest"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#consumeToIgnoreSecondCallTest">consumeToIgnoreSecondCallTest</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="chompBalancedThrowIllegalArgumentException"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#chompBalancedThrowIllegalArgumentException">chompBalancedThrowIllegalArgumentException</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="chompBalanced"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#chompBalanced">chompBalanced</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="testNestedQuotes"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#testNestedQuotes">testNestedQuotes</a></td><td>Success</td><td></td><td>0.025</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/22_TokenQueueTest.html">TokenQueueTest</a></td><td><a name="chompBalancedMatchesAsMuchAsPossible"></a><a href="org/jsoup/parser/22_TokenQueueTest.html#chompBalancedMatchesAsMuchAsPossible">chompBalancedMatchesAsMuchAsPossible</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testEndTagOpen"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testEndTagOpen">testEndTagOpen</a></td><td>Success</td><td></td><td>0.027</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testPublicIdentifiersWithWhitespace"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testPublicIdentifiersWithWhitespace">testPublicIdentifiersWithWhitespace</a></td><td>Success</td><td></td><td>0.006</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="handlesLessInTagThanAsNewTag"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#handlesLessInTagThanAsNewTag">handlesLessInTagThanAsNewTag</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testRCDATAEndTagName"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testRCDATAEndTagName">testRCDATAEndTagName</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testCharacterReferenceInRcdata"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testCharacterReferenceInRcdata">testCharacterReferenceInRcdata</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testRcdataLessthanSign"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testRcdataLessthanSign">testRcdataLessthanSign</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="ensureSearchArraysAreSorted"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#ensureSearchArraysAreSorted">ensureSearchArraysAreSorted</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testCommentEndCoverage"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testCommentEndCoverage">testCommentEndCoverage</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testBeforeTagName"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testBeforeTagName">testBeforeTagName</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testPublicAndSystemIdentifiersWithWhitespace"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testPublicAndSystemIdentifiersWithWhitespace">testPublicAndSystemIdentifiersWithWhitespace</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testSystemIdentifiersWithWhitespace"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testSystemIdentifiersWithWhitespace">testSystemIdentifiersWithWhitespace</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a name="testCommentEndBangCoverage"></a><a href="org/jsoup/parser/23_TokeniserStateTest.html#testCommentEndBangCoverage">testCommentEndBangCoverage</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="handleLargeComment"></a><a href="org/jsoup/parser/24_TokeniserTest.html#handleLargeComment">handleLargeComment</a></td><td>Success</td><td></td><td>0.027</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="handleLargeCdata"></a><a href="org/jsoup/parser/24_TokeniserTest.html#handleLargeCdata">handleLargeCdata</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="handleLargeTitle"></a><a href="org/jsoup/parser/24_TokeniserTest.html#handleLargeTitle">handleLargeTitle</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="bufferUpInAttributeVal"></a><a href="org/jsoup/parser/24_TokeniserTest.html#bufferUpInAttributeVal">bufferUpInAttributeVal</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="cp1252EntitiesProduceError"></a><a href="org/jsoup/parser/24_TokeniserTest.html#cp1252EntitiesProduceError">cp1252EntitiesProduceError</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="handleSuperLargeTagNames"></a><a href="org/jsoup/parser/24_TokeniserTest.html#handleSuperLargeTagNames">handleSuperLargeTagNames</a></td><td>Success</td><td></td><td>0.006</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="handleSuperLargeAttributeName"></a><a href="org/jsoup/parser/24_TokeniserTest.html#handleSuperLargeAttributeName">handleSuperLargeAttributeName</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="cp1252SubstitutionTable"></a><a href="org/jsoup/parser/24_TokeniserTest.html#cp1252SubstitutionTable">cp1252SubstitutionTable</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="cp1252Entities"></a><a href="org/jsoup/parser/24_TokeniserTest.html#cp1252Entities">cp1252Entities</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/24_TokeniserTest.html">TokeniserTest</a></td><td><a name="handleLargeText"></a><a href="org/jsoup/parser/24_TokeniserTest.html#handleLargeText">handleLargeText</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testPopToClose"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testPopToClose">testPopToClose</a></td><td>Success</td><td></td><td>0.021</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="handlesXmlDeclarationAsDeclaration"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#handlesXmlDeclarationAsDeclaration">handlesXmlDeclarationAsDeclaration</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="dropsDuplicateAttributes"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#dropsDuplicateAttributes">dropsDuplicateAttributes</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="caseSensitiveDeclaration"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#caseSensitiveDeclaration">caseSensitiveDeclaration</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="handlesDodgyXmlDecl"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#handlesDodgyXmlDecl">handlesDodgyXmlDecl</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="handlesLTinScript"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#handlesLTinScript">handlesLTinScript</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testSupplyParserToJsoupClass"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testSupplyParserToJsoupClass">testSupplyParserToJsoupClass</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="normalizesDiscordantTags"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#normalizesDiscordantTags">normalizesDiscordantTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testDoesNotForceSelfClosingKnownTags"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testDoesNotForceSelfClosingKnownTags">testDoesNotForceSelfClosingKnownTags</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testCommentAndDocType"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testCommentAndDocType">testCommentAndDocType</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testSimpleXmlParse"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testSimpleXmlParse">testSimpleXmlParse</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="xmlFragment"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#xmlFragment">xmlFragment</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="appendPreservesCaseByDefault"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#appendPreservesCaseByDefault">appendPreservesCaseByDefault</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testDetectCharsetEncodingDeclaration"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testDetectCharsetEncodingDeclaration">testDetectCharsetEncodingDeclaration</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="roundTripsCdata"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#roundTripsCdata">roundTripsCdata</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="canNormalizeCase"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#canNormalizeCase">canNormalizeCase</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="cdataPreservesWhiteSpace"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#cdataPreservesWhiteSpace">cdataPreservesWhiteSpace</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="preservesCaseByDefault"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#preservesCaseByDefault">preservesCaseByDefault</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testParseDeclarationAttributes"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testParseDeclarationAttributes">testParseDeclarationAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testSupplyParserToDataStream"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testSupplyParserToDataStream">testSupplyParserToDataStream</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="xmlParseDefaultsToHtmlOutputSyntax"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#xmlParseDefaultsToHtmlOutputSyntax">xmlParseDefaultsToHtmlOutputSyntax</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testCreatesValidProlog"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testCreatesValidProlog">testCreatesValidProlog</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a name="testDoesHandleEOFInTag"></a><a href="org/jsoup/parser/25_XmlTreeBuilderTest.html#testDoesHandleEOFInTag">testDoesHandleEOFInTag</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testHandlesEmptyAttributes"></a><a href="org/jsoup/safety/26_CleanerTest.html#testHandlesEmptyAttributes">testHandlesEmptyAttributes</a></td><td>Success</td><td></td><td>0.025</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="basicBehaviourTest"></a><a href="org/jsoup/safety/26_CleanerTest.html#basicBehaviourTest">basicBehaviourTest</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="handlesCustomProtocols"></a><a href="org/jsoup/safety/26_CleanerTest.html#handlesCustomProtocols">handlesCustomProtocols</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testIsValidBodyHtml"></a><a href="org/jsoup/safety/26_CleanerTest.html#testIsValidBodyHtml">testIsValidBodyHtml</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="handlesNoHrefAttribute"></a><a href="org/jsoup/safety/26_CleanerTest.html#handlesNoHrefAttribute">handlesNoHrefAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="preservesRelativeLinksIfConfigured"></a><a href="org/jsoup/safety/26_CleanerTest.html#preservesRelativeLinksIfConfigured">preservesRelativeLinksIfConfigured</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testCleanAnchorProtocol"></a><a href="org/jsoup/safety/26_CleanerTest.html#testCleanAnchorProtocol">testCleanAnchorProtocol</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="bailsIfRemovingProtocolThatsNotSet"></a><a href="org/jsoup/safety/26_CleanerTest.html#bailsIfRemovingProtocolThatsNotSet">bailsIfRemovingProtocolThatsNotSet</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testDropScript"></a><a href="org/jsoup/safety/26_CleanerTest.html#testDropScript">testDropScript</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testRemoveTags"></a><a href="org/jsoup/safety/26_CleanerTest.html#testRemoveTags">testRemoveTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testRemoveAttributes"></a><a href="org/jsoup/safety/26_CleanerTest.html#testRemoveAttributes">testRemoveAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testRemoveEnforcedAttributes"></a><a href="org/jsoup/safety/26_CleanerTest.html#testRemoveEnforcedAttributes">testRemoveEnforcedAttributes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testRelaxed"></a><a href="org/jsoup/safety/26_CleanerTest.html#testRelaxed">testRelaxed</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="resolvesRelativeLinks"></a><a href="org/jsoup/safety/26_CleanerTest.html#resolvesRelativeLinks">resolvesRelativeLinks</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testDropComments"></a><a href="org/jsoup/safety/26_CleanerTest.html#testDropComments">testDropComments</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="handlesFramesets"></a><a href="org/jsoup/safety/26_CleanerTest.html#handlesFramesets">handlesFramesets</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="handlesControlCharactersAfterTagName"></a><a href="org/jsoup/safety/26_CleanerTest.html#handlesControlCharactersAfterTagName">handlesControlCharactersAfterTagName</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="simpleBehaviourTest2"></a><a href="org/jsoup/safety/26_CleanerTest.html#simpleBehaviourTest2">simpleBehaviourTest2</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testRemoveProtocols"></a><a href="org/jsoup/safety/26_CleanerTest.html#testRemoveProtocols">testRemoveProtocols</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testDropImageScript"></a><a href="org/jsoup/safety/26_CleanerTest.html#testDropImageScript">testDropImageScript</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="simpleBehaviourTest"></a><a href="org/jsoup/safety/26_CleanerTest.html#simpleBehaviourTest">simpleBehaviourTest</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="handlesAttributesWithNoValue"></a><a href="org/jsoup/safety/26_CleanerTest.html#handlesAttributesWithNoValue">handlesAttributesWithNoValue</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="dropsUnresolvableRelativeLinks"></a><a href="org/jsoup/safety/26_CleanerTest.html#dropsUnresolvableRelativeLinks">dropsUnresolvableRelativeLinks</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testScriptTagInWhiteList"></a><a href="org/jsoup/safety/26_CleanerTest.html#testScriptTagInWhiteList">testScriptTagInWhiteList</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="addsTagOnAttributesIfNotSet"></a><a href="org/jsoup/safety/26_CleanerTest.html#addsTagOnAttributesIfNotSet">addsTagOnAttributesIfNotSet</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testDropsUnknownTags"></a><a href="org/jsoup/safety/26_CleanerTest.html#testDropsUnknownTags">testDropsUnknownTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testCleanJavascriptHref"></a><a href="org/jsoup/safety/26_CleanerTest.html#testCleanJavascriptHref">testCleanJavascriptHref</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="basicWithImagesTest"></a><a href="org/jsoup/safety/26_CleanerTest.html#basicWithImagesTest">basicWithImagesTest</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="whitelistedProtocolShouldBeRetained"></a><a href="org/jsoup/safety/26_CleanerTest.html#whitelistedProtocolShouldBeRetained">whitelistedProtocolShouldBeRetained</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testDropXmlProc"></a><a href="org/jsoup/safety/26_CleanerTest.html#testDropXmlProc">testDropXmlProc</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="testIsValidDocument"></a><a href="org/jsoup/safety/26_CleanerTest.html#testIsValidDocument">testIsValidDocument</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="handlesAllPseudoTag"></a><a href="org/jsoup/safety/26_CleanerTest.html#handlesAllPseudoTag">handlesAllPseudoTag</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="supplyOutputSettings"></a><a href="org/jsoup/safety/26_CleanerTest.html#supplyOutputSettings">supplyOutputSettings</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/safety/26_CleanerTest.html">CleanerTest</a></td><td><a name="cleansInternationalText"></a><a href="org/jsoup/safety/26_CleanerTest.html#cleansInternationalText">cleansInternationalText</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthLastOfType_advanced"></a><a href="org/jsoup/select/27_CssTest.html#nthLastOfType_advanced">nthLastOfType_advanced</a></td><td>Success</td><td></td><td>0.028</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthChild_advanced"></a><a href="org/jsoup/select/27_CssTest.html#nthChild_advanced">nthChild_advanced</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthOfType_advanced"></a><a href="org/jsoup/select/27_CssTest.html#nthOfType_advanced">nthOfType_advanced</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthOfType_simple"></a><a href="org/jsoup/select/27_CssTest.html#nthOfType_simple">nthOfType_simple</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="firstOfType"></a><a href="org/jsoup/select/27_CssTest.html#firstOfType">firstOfType</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="firstChild"></a><a href="org/jsoup/select/27_CssTest.html#firstChild">firstChild</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthLastOfType_simple"></a><a href="org/jsoup/select/27_CssTest.html#nthLastOfType_simple">nthLastOfType_simple</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="root"></a><a href="org/jsoup/select/27_CssTest.html#root">root</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="onlyOfType"></a><a href="org/jsoup/select/27_CssTest.html#onlyOfType">onlyOfType</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthLastChild_simple"></a><a href="org/jsoup/select/27_CssTest.html#nthLastChild_simple">nthLastChild_simple</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="empty"></a><a href="org/jsoup/select/27_CssTest.html#empty">empty</a></td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="onlyChild"></a><a href="org/jsoup/select/27_CssTest.html#onlyChild">onlyChild</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthChild_simple"></a><a href="org/jsoup/select/27_CssTest.html#nthChild_simple">nthChild_simple</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthLastChild_advanced"></a><a href="org/jsoup/select/27_CssTest.html#nthLastChild_advanced">nthLastChild_advanced</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="lastOfType"></a><a href="org/jsoup/select/27_CssTest.html#lastOfType">lastOfType</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="lastChild"></a><a href="org/jsoup/select/27_CssTest.html#lastChild">lastChild</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/27_CssTest.html">CssTest</a></td><td><a name="nthOfType_unknownTag"></a><a href="org/jsoup/select/27_CssTest.html#nthOfType_unknownTag">nthOfType_unknownTag</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="classWithHyphen"></a><a href="org/jsoup/select/28_ElementsTest.html#classWithHyphen">classWithHyphen</a></td><td>Success</td><td></td><td>0.025</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="before"></a><a href="org/jsoup/select/28_ElementsTest.html#before">before</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="filter"></a><a href="org/jsoup/select/28_ElementsTest.html#filter">filter</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="eachAttr"></a><a href="org/jsoup/select/28_ElementsTest.html#eachAttr">eachAttr</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="eachText"></a><a href="org/jsoup/select/28_ElementsTest.html#eachText">eachText</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="absAttr"></a><a href="org/jsoup/select/28_ElementsTest.html#absAttr">absAttr</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="remove"></a><a href="org/jsoup/select/28_ElementsTest.html#remove">remove</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="unwrap"></a><a href="org/jsoup/select/28_ElementsTest.html#unwrap">unwrap</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="parents"></a><a href="org/jsoup/select/28_ElementsTest.html#parents">parents</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="unwrapP"></a><a href="org/jsoup/select/28_ElementsTest.html#unwrapP">unwrapP</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="unwrapKeepsSpace"></a><a href="org/jsoup/select/28_ElementsTest.html#unwrapKeepsSpace">unwrapKeepsSpace</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="eq"></a><a href="org/jsoup/select/28_ElementsTest.html#eq">eq</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="is"></a><a href="org/jsoup/select/28_ElementsTest.html#is">is</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="not"></a><a href="org/jsoup/select/28_ElementsTest.html#not">not</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="val"></a><a href="org/jsoup/select/28_ElementsTest.html#val">val</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="attr"></a><a href="org/jsoup/select/28_ElementsTest.html#attr">attr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="html"></a><a href="org/jsoup/select/28_ElementsTest.html#html">html</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="text"></a><a href="org/jsoup/select/28_ElementsTest.html#text">text</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="wrap"></a><a href="org/jsoup/select/28_ElementsTest.html#wrap">wrap</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="hasClassCaseInsensitive"></a><a href="org/jsoup/select/28_ElementsTest.html#hasClassCaseInsensitive">hasClassCaseInsensitive</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="after"></a><a href="org/jsoup/select/28_ElementsTest.html#after">after</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="empty"></a><a href="org/jsoup/select/28_ElementsTest.html#empty">empty</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="forms"></a><a href="org/jsoup/select/28_ElementsTest.html#forms">forms</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="siblings"></a><a href="org/jsoup/select/28_ElementsTest.html#siblings">siblings</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="attributes"></a><a href="org/jsoup/select/28_ElementsTest.html#attributes">attributes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="hasAttr"></a><a href="org/jsoup/select/28_ElementsTest.html#hasAttr">hasAttr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="hasText"></a><a href="org/jsoup/select/28_ElementsTest.html#hasText">hasText</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="hasAbsAttr"></a><a href="org/jsoup/select/28_ElementsTest.html#hasAbsAttr">hasAbsAttr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="classes"></a><a href="org/jsoup/select/28_ElementsTest.html#classes">classes</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="traverse"></a><a href="org/jsoup/select/28_ElementsTest.html#traverse">traverse</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="wrapDiv"></a><a href="org/jsoup/select/28_ElementsTest.html#wrapDiv">wrapDiv</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="outerHtml"></a><a href="org/jsoup/select/28_ElementsTest.html#outerHtml">outerHtml</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="tagNameSet"></a><a href="org/jsoup/select/28_ElementsTest.html#tagNameSet">tagNameSet</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/28_ElementsTest.html">ElementsTest</a></td><td><a name="setHtml"></a><a href="org/jsoup/select/28_ElementsTest.html#setHtml">setHtml</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/29_QueryParserTest.html">QueryParserTest</a></td><td><a name="testParsesSingleQuoteInContains"></a><a href="org/jsoup/select/29_QueryParserTest.html#testParsesSingleQuoteInContains">testParsesSingleQuoteInContains</a></td><td>Success</td><td></td><td>0.003</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/29_QueryParserTest.html">QueryParserTest</a></td><td><a name="testOrGetsCorrectPrecedence"></a><a href="org/jsoup/select/29_QueryParserTest.html#testOrGetsCorrectPrecedence">testOrGetsCorrectPrecedence</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/29_QueryParserTest.html">QueryParserTest</a></td><td><a name="testParsesMultiCorrectly"></a><a href="org/jsoup/select/29_QueryParserTest.html#testParsesMultiCorrectly">testParsesMultiCorrectly</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/29_QueryParserTest.html">QueryParserTest</a></td><td><a name="exceptionOnUncloseAttribute"></a><a href="org/jsoup/select/29_QueryParserTest.html#exceptionOnUncloseAttribute">exceptionOnUncloseAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="containsData"></a><a href="org/jsoup/select/30_SelectorTest.html#containsData">containsData</a></td><td>Success</td><td></td><td>0.026</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByAttributeStarting"></a><a href="org/jsoup/select/30_SelectorTest.html#testByAttributeStarting">testByAttributeStarting</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="deeperDescendant"></a><a href="org/jsoup/select/30_SelectorTest.html#deeperDescendant">deeperDescendant</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="selectSameElements"></a><a href="org/jsoup/select/30_SelectorTest.html#selectSameElements">selectSameElements</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoEquals"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoEquals">testPseudoEquals</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByClassCaseInsensitive"></a><a href="org/jsoup/select/30_SelectorTest.html#testByClassCaseInsensitive">testByClassCaseInsensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByAttributeRegexCharacterClass"></a><a href="org/jsoup/select/30_SelectorTest.html#testByAttributeRegexCharacterClass">testByAttributeRegexCharacterClass</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testAllWithClass"></a><a href="org/jsoup/select/30_SelectorTest.html#testAllWithClass">testAllWithClass</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByTag"></a><a href="org/jsoup/select/30_SelectorTest.html#testByTag">testByTag</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testById"></a><a href="org/jsoup/select/30_SelectorTest.html#testById">testById</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="findBetweenSpan"></a><a href="org/jsoup/select/30_SelectorTest.html#findBetweenSpan">findBetweenSpan</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="descendant"></a><a href="org/jsoup/select/30_SelectorTest.html#descendant">descendant</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="handlesCommasInSelector"></a><a href="org/jsoup/select/30_SelectorTest.html#handlesCommasInSelector">handlesCommasInSelector</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="notAll"></a><a href="org/jsoup/select/30_SelectorTest.html#notAll">notAll</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByAttribute"></a><a href="org/jsoup/select/30_SelectorTest.html#testByAttribute">testByAttribute</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="selectClassWithSpace"></a><a href="org/jsoup/select/30_SelectorTest.html#selectClassWithSpace">selectClassWithSpace</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="notAdjacent"></a><a href="org/jsoup/select/30_SelectorTest.html#notAdjacent">notAdjacent</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testCombinedWithContains"></a><a href="org/jsoup/select/30_SelectorTest.html#testCombinedWithContains">testCombinedWithContains</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="containsWithQuote"></a><a href="org/jsoup/select/30_SelectorTest.html#containsWithQuote">containsWithQuote</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoGreaterThan"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoGreaterThan">testPseudoGreaterThan</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testAllElements"></a><a href="org/jsoup/select/30_SelectorTest.html#testAllElements">testAllElements</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByAttributeRegex"></a><a href="org/jsoup/select/30_SelectorTest.html#testByAttributeRegex">testByAttributeRegex</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="mixCombinator"></a><a href="org/jsoup/select/30_SelectorTest.html#mixCombinator">mixCombinator</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testRelaxedTags"></a><a href="org/jsoup/select/30_SelectorTest.html#testRelaxedTags">testRelaxedTags</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoBetween"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoBetween">testPseudoBetween</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoLessThan"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoLessThan">testPseudoLessThan</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testGroupOr"></a><a href="org/jsoup/select/30_SelectorTest.html#testGroupOr">testGroupOr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByClass"></a><a href="org/jsoup/select/30_SelectorTest.html#testByClass">testByClass</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="and"></a><a href="org/jsoup/select/30_SelectorTest.html#and">and</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="attributeWithBrackets"></a><a href="org/jsoup/select/30_SelectorTest.html#attributeWithBrackets">attributeWithBrackets</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPsuedoContainsWithParentheses"></a><a href="org/jsoup/select/30_SelectorTest.html#testPsuedoContainsWithParentheses">testPsuedoContainsWithParentheses</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testGroupOrAttribute"></a><a href="org/jsoup/select/30_SelectorTest.html#testGroupOrAttribute">testGroupOrAttribute</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="caseInsensitive"></a><a href="org/jsoup/select/30_SelectorTest.html#caseInsensitive">caseInsensitive</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="selectFirst"></a><a href="org/jsoup/select/30_SelectorTest.html#selectFirst">selectFirst</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="containsOwn"></a><a href="org/jsoup/select/30_SelectorTest.html#containsOwn">containsOwn</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testCharactersInIdAndClass"></a><a href="org/jsoup/select/30_SelectorTest.html#testCharactersInIdAndClass">testCharactersInIdAndClass</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="splitOnBr"></a><a href="org/jsoup/select/30_SelectorTest.html#splitOnBr">splitOnBr</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="multiChildDescent"></a><a href="org/jsoup/select/30_SelectorTest.html#multiChildDescent">multiChildDescent</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="adjacentSiblingsWithId"></a><a href="org/jsoup/select/30_SelectorTest.html#adjacentSiblingsWithId">adjacentSiblingsWithId</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testMatches"></a><a href="org/jsoup/select/30_SelectorTest.html#testMatches">testMatches</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="matchText"></a><a href="org/jsoup/select/30_SelectorTest.html#matchText">matchText</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="selectFirstWithOr"></a><a href="org/jsoup/select/30_SelectorTest.html#selectFirstWithOr">selectFirstWithOr</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="matchTextAttributes"></a><a href="org/jsoup/select/30_SelectorTest.html#matchTextAttributes">matchTextAttributes</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="parentWithClassChild"></a><a href="org/jsoup/select/30_SelectorTest.html#parentWithClassChild">parentWithClassChild</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="generalSiblings"></a><a href="org/jsoup/select/30_SelectorTest.html#generalSiblings">generalSiblings</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="parentChildStar"></a><a href="org/jsoup/select/30_SelectorTest.html#parentChildStar">parentChildStar</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoCombined"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoCombined">testPseudoCombined</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoContains"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoContains">testPseudoContains</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testPseudoHas"></a><a href="org/jsoup/select/30_SelectorTest.html#testPseudoHas">testPseudoHas</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="notClass"></a><a href="org/jsoup/select/30_SelectorTest.html#notClass">notClass</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="notParas"></a><a href="org/jsoup/select/30_SelectorTest.html#notParas">notParas</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="selectSupplementaryCharacter"></a><a href="org/jsoup/select/30_SelectorTest.html#selectSupplementaryCharacter">selectSupplementaryCharacter</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="adjacentSiblings"></a><a href="org/jsoup/select/30_SelectorTest.html#adjacentSiblings">adjacentSiblings</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testNamespacedTag"></a><a href="org/jsoup/select/30_SelectorTest.html#testNamespacedTag">testNamespacedTag</a></td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testNestedHas"></a><a href="org/jsoup/select/30_SelectorTest.html#testNestedHas">testNestedHas</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="parentChildElement"></a><a href="org/jsoup/select/30_SelectorTest.html#parentChildElement">parentChildElement</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testByAttributeRegexCombined"></a><a href="org/jsoup/select/30_SelectorTest.html#testByAttributeRegexCombined">testByAttributeRegexCombined</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="matchesOwn"></a><a href="org/jsoup/select/30_SelectorTest.html#matchesOwn">matchesOwn</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testSupportsLeadingCombinator"></a><a href="org/jsoup/select/30_SelectorTest.html#testSupportsLeadingCombinator">testSupportsLeadingCombinator</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="mixCombinatorGroup"></a><a href="org/jsoup/select/30_SelectorTest.html#mixCombinatorGroup">mixCombinatorGroup</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="testWildcardNamespacedTag"></a><a href="org/jsoup/select/30_SelectorTest.html#testWildcardNamespacedTag">testWildcardNamespacedTag</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/30_SelectorTest.html">SelectorTest</a></td><td><a name="selectFirstWithAnd"></a><a href="org/jsoup/select/30_SelectorTest.html#selectFirstWithAnd">selectFirstWithAnd</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/31_TraversorTest.html">TraversorTest</a></td><td><a name="filterStop"></a><a href="org/jsoup/select/31_TraversorTest.html#filterStop">filterStop</a></td><td>Success</td><td></td><td>0.026</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/31_TraversorTest.html">TraversorTest</a></td><td><a name="filterVisit"></a><a href="org/jsoup/select/31_TraversorTest.html#filterVisit">filterVisit</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/31_TraversorTest.html">TraversorTest</a></td><td><a name="filterSkipChildren"></a><a href="org/jsoup/select/31_TraversorTest.html#filterSkipChildren">filterSkipChildren</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/31_TraversorTest.html">TraversorTest</a></td><td><a name="filterSkipEntirely"></a><a href="org/jsoup/select/31_TraversorTest.html#filterSkipEntirely">filterSkipEntirely</a></td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a href="org/jsoup/select/31_TraversorTest.html">TraversorTest</a></td><td><a name="filterRemove"></a><a href="org/jsoup/select/31_TraversorTest.html#filterRemove">filterRemove</a></td><td>Success</td><td></td><td>0.000</td>
            </tr>
        </table>
    </body>
</html>
