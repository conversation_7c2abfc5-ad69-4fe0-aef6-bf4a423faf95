<html xmlns:string="xalan://java.lang.String" xmlns:lxslt="http://xml.apache.org/xslt">
    <head>
        <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Unit Test Results: org.jsoup.parser.HtmlParserTest</title>
        <link rel="stylesheet" type="text/css" title="Style" href="../../../stylesheet.css">
        <script type="text/javascript" language="JavaScript">
        var TestCases = new Array();
        var cur;
        
    cur = TestCases['org.jsoup.parser.HtmlParserTest'] = new Array();
    
        cur['ant.core.lib'] = '/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar';
    
        cur['ant.file'] = '/Users/<USER>/Desktop/jsoup/build.xml';
    
        cur['ant.file.jsoup'] = '/Users/<USER>/Desktop/jsoup/build.xml';
    
        cur['ant.file.type'] = 'file';
    
        cur['ant.file.type.jsoup'] = 'file';
    
        cur['ant.home'] = '/opt/homebrew/Cellar/ant/1.10.15_1/libexec';
    
        cur['ant.java.version'] = '23';
    
        cur['ant.library.dir'] = '/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib';
    
        cur['ant.project.default-target'] = 'build';
    
        cur['ant.project.invoked-targets'] = 'clean,test';
    
        cur['ant.project.name'] = 'jsoup';
    
        cur['ant.version'] = 'Apache Ant(TM) version 1.10.15 compiled on August 25 2024';
    
        cur['apple.awt.application.name'] = 'JUnitTestRunner';
    
        cur['basedir'] = '/Users/<USER>/Desktop/jsoup';
    
        cur['debuglevel'] = 'source,lines,vars';
    
        cur['env.__CF_USER_TEXT_ENCODING'] = '0x1F5:0x0:0x0';
    
        cur['env.__CFBundleIdentifier'] = 'com.microsoft.VSCode';
    
        cur['env.COLORTERM'] = 'truecolor';
    
        cur['env.COMMAND_MODE'] = 'unix2003';
    
        cur['env.DISPLAY'] = '/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0';
    
        cur['env.GEM_HOME'] = '/Users/<USER>/.gem/ruby/3.4.1';
    
        cur['env.GEM_PATH'] = '/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0';
    
        cur['env.GEM_ROOT'] = '/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0';
    
        cur['env.GIT_ASKPASS'] = '/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh';
    
        cur['env.GIT_PAGER'] = 'cat';
    
        cur['env.HOME'] = '/Users/<USER>';
    
        cur['env.HOMEBREW_CELLAR'] = '/opt/homebrew/Cellar';
    
        cur['env.HOMEBREW_PREFIX'] = '/opt/homebrew';
    
        cur['env.HOMEBREW_REPOSITORY'] = '/opt/homebrew';
    
        cur['env.INFOPATH'] = '/opt/homebrew/share/info:';
    
        cur['env.JAVA_HOME'] = '/opt/homebrew/opt/openjdk';
    
        cur['env.LANG'] = 'en_US.UTF-8';
    
        cur['env.LESS'] = '-FX';
    
        cur['env.LOGNAME'] = 'justin';
    
        cur['env.MallocNanoZone'] = '0';
    
        cur['env.ORIGINAL_XDG_CURRENT_DESKTOP'] = 'undefined';
    
        cur['env.PAGER'] = 'cat';
    
        cur['env.PATH'] = '/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts';
    
        cur['env.PROMPT_COMMAND'] = 'echo "$(pwd)" > \'/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt\'\n__augment_output_end_marker';
    
        cur['env.PWD'] = '/Users/<USER>/Desktop/jsoup';
    
        cur['env.RUBY_ENGINE'] = 'ruby';
    
        cur['env.RUBY_ROOT'] = '/Users/<USER>/.rubies/ruby-3.4.1';
    
        cur['env.RUBY_VERSION'] = '3.4.1';
    
        cur['env.RUBYOPT'] = '';
    
        cur['env.SCRIPT'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log';
    
        cur['env.SHELL'] = '/bin/bash';
    
        cur['env.SHLVL'] = '2';
    
        cur['env.SSH_AUTH_SOCK'] = '/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners';
    
        cur['env.TERM'] = 'xterm-256color';
    
        cur['env.TERM_PROGRAM'] = 'vscode';
    
        cur['env.TERM_PROGRAM_VERSION'] = '1.103.2';
    
        cur['env.TMPDIR'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/';
    
        cur['env.USER'] = 'justin';
    
        cur['env.VSCODE_GIT_ASKPASS_EXTRA_ARGS'] = '';
    
        cur['env.VSCODE_GIT_ASKPASS_MAIN'] = '/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js';
    
        cur['env.VSCODE_GIT_ASKPASS_NODE'] = '/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)';
    
        cur['env.VSCODE_GIT_IPC_HANDLE'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock';
    
        cur['env.XPC_FLAGS'] = '0x0';
    
        cur['env.XPC_SERVICE_NAME'] = '0';
    
        cur['file.encoding'] = 'UTF-8';
    
        cur['file.separator'] = '/';
    
        cur['ftp.nonProxyHosts'] = 'local|*.local|169.254/16|*.169.254/16';
    
        cur['http.nonProxyHosts'] = 'local|*.local|169.254/16|*.169.254/16';
    
        cur['java.class.path'] = '/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar';
    
        cur['java.class.version'] = '67.0';
    
        cur['java.home'] = '/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home';
    
        cur['java.io.tmpdir'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/';
    
        cur['java.library.path'] = '/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.';
    
        cur['java.runtime.name'] = 'OpenJDK Runtime Environment';
    
        cur['java.runtime.version'] = '23.0.2';
    
        cur['java.specification.name'] = 'Java Platform API Specification';
    
        cur['java.specification.vendor'] = 'Oracle Corporation';
    
        cur['java.specification.version'] = '23';
    
        cur['java.vendor'] = 'Homebrew';
    
        cur['java.vendor.url'] = 'https://github.com/Homebrew/homebrew-core/issues';
    
        cur['java.vendor.url.bug'] = 'https://github.com/Homebrew/homebrew-core/issues';
    
        cur['java.vendor.version'] = 'Homebrew';
    
        cur['java.version'] = '23.0.2';
    
        cur['java.version.date'] = '2025-01-21';
    
        cur['java.vm.compressedOopsMode'] = 'Zero based';
    
        cur['java.vm.info'] = 'mixed mode, sharing';
    
        cur['java.vm.name'] = 'OpenJDK 64-Bit Server VM';
    
        cur['java.vm.specification.name'] = 'Java Virtual Machine Specification';
    
        cur['java.vm.specification.vendor'] = 'Oracle Corporation';
    
        cur['java.vm.specification.version'] = '23';
    
        cur['java.vm.vendor'] = 'Homebrew';
    
        cur['java.vm.version'] = '23.0.2';
    
        cur['jdk.debug'] = 'release';
    
        cur['junit.output.dir'] = 'ant-target/reports';
    
        cur['line.separator'] = '\n';
    
        cur['native.encoding'] = 'UTF-8';
    
        cur['os.arch'] = 'aarch64';
    
        cur['os.name'] = 'Mac OS X';
    
        cur['os.version'] = '15.6.1';
    
        cur['path.separator'] = ':';
    
        cur['socksNonProxyHosts'] = 'local|*.local|169.254/16|*.169.254/16';
    
        cur['source'] = '1.8';
    
        cur['stderr.encoding'] = 'UTF-8';
    
        cur['stdout.encoding'] = 'UTF-8';
    
        cur['sun.arch.data.model'] = '64';
    
        cur['sun.boot.library.path'] = '/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib';
    
        cur['sun.cpu.endian'] = 'little';
    
        cur['sun.io.unicode.encoding'] = 'UnicodeBig';
    
        cur['sun.java.command'] = 'org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.parser.HtmlParserTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.parser.HtmlParserTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher2127338097888008035.properties propsfile=/Users/<USER>/Desktop/jsoup/junit11267010801563507682.properties';
    
        cur['sun.java.launcher'] = 'SUN_STANDARD';
    
        cur['sun.jnu.encoding'] = 'UTF-8';
    
        cur['sun.management.compiler'] = 'HotSpot 64-Bit Tiered Compilers';
    
        cur['target'] = '1.8';
    
        cur['user.country'] = 'US';
    
        cur['user.dir'] = '/Users/<USER>/Desktop/jsoup';
    
        cur['user.home'] = '/Users/<USER>';
    
        cur['user.language'] = 'en';
    
        cur['user.name'] = 'justin';
    </script><script type="text/javascript" language="JavaScript">
        function displayProperties (name) {
          var win = window.open('','JUnitSystemProperties','scrollbars=1,resizable=1');
          var doc = win.document;
          doc.open();
          doc.write("<html><head><title>Properties of " + name + "</title>");
          doc.write("<style type=\"text/css\">");
          doc.write("body {font:normal 68% verdana,arial,helvetica; color:#000000; }");
          doc.write("table tr td, table tr th { font-size: 68%; }");
          doc.write("table.properties { border-collapse:collapse; border-left:solid 1 #cccccc; border-top:solid 1 #cccccc; padding:5px; }");
          doc.write("table.properties th { text-align:left; border-right:solid 1 #cccccc; border-bottom:solid 1 #cccccc; background-color:#eeeeee; }");
          doc.write("table.properties td { font:normal; text-align:left; border-right:solid 1 #cccccc; border-bottom:solid 1 #cccccc; background-color:#fffffff; }");
          doc.write("h3 { margin-bottom: 0.5em; font: bold 115% verdana,arial,helvetica }");
          doc.write("</style>");
          doc.write("</head><body>");
          doc.write("<h3>Properties of " + name + "</h3>");
          doc.write("<div align=\"right\"><a href=\"javascript:window.close();\">Close</a></div>");
          doc.write("<table class='properties'>");
          doc.write("<tr><th>Name</th><th>Value</th></tr>");
          for (prop in TestCases[name]) {
            doc.write("<tr><th>" + prop + "</th><td>" + TestCases[name][prop] + "</td></tr>");
          }
          doc.write("</table>");
          doc.write("</body></html>");
          doc.close();
          win.focus();
        }
      
      </script>
    </head>
    <body>
        <h1>Unit Test Results.</h1>
        <table width="100%">
            <tr>
                <td align="left"></td><td align="right">Designed for use with <a href="https://www.junit.org/">JUnit</a> and <a href="https://ant.apache.org/">Ant</a>.</td>
            </tr>
        </table>
        <hr size="1">
        <h3>Class org.jsoup.parser.HtmlParserTest</h3>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th width="80%">Name</th><th>Tests</th><th>Errors</th><th>Failures</th><th>Skipped</th><th nowrap>Time(s)</th><th nowrap>Time Stamp</th><th>Host</th>
            </tr>
            <tr valign="top" class="Pass">
                <td><a title="Display all tests" href="16_HtmlParserTest.html">HtmlParserTest</a></td><td><a title="Display all tests" href="16_HtmlParserTest.html">132</a></td><td>0</td><td>0</td><td>0</td><td>0.333</td><td>2025-09-14T06:47:03</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
        </table>
        <h2>Tests</h2>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th>Name</th><th>Status</th><th width="80%">Type</th><th nowrap>Time(s)</th>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="preservedCaseLinksCantNest"></a>preservedCaseLinksCantNest</td><td>Success</td><td></td><td>0.023</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="normalisesHeadlessBody"></a>normalisesHeadlessBody</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesUnterminatedOption"></a>parsesUnterminatedOption</td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesProtocolRelativeUrl"></a>handlesProtocolRelativeUrl</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="dropsUnterminatedTag"></a>dropsUnterminatedTag</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="canPreserveAttributeCase"></a>canPreserveAttributeCase</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesNullInComments"></a>handlesNullInComments</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesTextAfterData"></a>handlesTextAfterData</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="dropsUnterminatedAttribute"></a>dropsUnterminatedAttribute</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testNormalisesIsIndex"></a>testNormalisesIsIndex</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="noTableDirectInTable"></a>noTableDirectInTable</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testSupportsPartiallyNonAsciiTags"></a>testSupportsPartiallyNonAsciiTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testSpaceAfterTag"></a>testSpaceAfterTag</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesXmlDeclarationAsBogusComment"></a>handlesXmlDeclarationAsBogusComment</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="normalisesDocument"></a>normalisesDocument</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="createsStructureFromBodySnippet"></a>createsStructureFromBodySnippet</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="retainsAttributesOfDifferentCaseIfSensitive"></a>retainsAttributesOfDifferentCaseIfSensitive</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="canPreserveTagCase"></a>canPreserveTagCase</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="preservesSpaceInScript"></a>preservesSpaceInScript</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesXmlDeclAndCommentsBeforeDoctype"></a>handlesXmlDeclAndCommentsBeforeDoctype</td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testHtmlLowerCase"></a>testHtmlLowerCase</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesDeepStack"></a>handlesDeepStack</td><td>Success</td><td></td><td>0.162</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesSimpleDocument"></a>parsesSimpleDocument</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="doesNotCreateImplicitLists"></a>doesNotCreateImplicitLists</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesUnterminatedTextarea"></a>parsesUnterminatedTextarea</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testSupportsNonAsciiTags"></a>testSupportsNonAsciiTags</td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="dropsDuplicateAttributes"></a>dropsDuplicateAttributes</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesWhatWgExpensesTableExample"></a>handlesWhatWgExpensesTableExample</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesImplicitCaptionClose"></a>handlesImplicitCaptionClose</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesQuotesInCommentsInScripts"></a>handlesQuotesInCommentsInScripts</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesInputInTable"></a>handlesInputInTable</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesInvalidStartTags"></a>handlesInvalidStartTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesEscapedData"></a>handlesEscapedData</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="fallbackToUtfIfCantEncode"></a>fallbackToUtfIfCantEncode</td><td>Success</td><td></td><td>0.005</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="reconstructFormattingElements"></a>reconstructFormattingElements</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="createsDocumentStructure"></a>createsDocumentStructure</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesCdataInScript"></a>handlesCdataInScript</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedTitle"></a>handlesUnclosedTitle</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="caseInsensitiveParseTree"></a>caseInsensitiveParseTree</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handleNullContextInParseFragment"></a>handleNullContextInParseFragment</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesUnterminatedComments"></a>parsesUnterminatedComments</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="emptyTdTag"></a>emptyTdTag</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="caseSensitiveParseTree"></a>caseSensitiveParseTree</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testFragment"></a>testFragment</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedScriptAtEof"></a>handlesUnclosedScriptAtEof</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesNewlinesAndWhitespaceInTag"></a>handlesNewlinesAndWhitespaceInTag</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesDataOnlyTags"></a>handlesDataOnlyTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="normalizesDiscordantTags"></a>normalizesDiscordantTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesCdataAcrossBuffer"></a>handlesCdataAcrossBuffer</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="noImplicitFormForTextAreas"></a>noImplicitFormForTextAreas</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="commentBeforeHtml"></a>commentBeforeHtml</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedCdataAtEOF"></a>handlesUnclosedCdataAtEOF</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesCdata"></a>handlesCdata</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesKnownEmptyBlocks"></a>handlesKnownEmptyBlocks</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="createsFormElements"></a>createsFormElements</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="noErrorsByDefault"></a>noErrorsByDefault</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testHeaderContents"></a>testHeaderContents</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testRelaxedTags"></a>testRelaxedTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesKnownEmptyStyle"></a>handlesKnownEmptyStyle</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesKnownEmptyTitle"></a>handlesKnownEmptyTitle</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="preservesSpaceInTextArea"></a>preservesSpaceInTextArea</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnexpectedMarkupInTables"></a>handlesUnexpectedMarkupInTables</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesKnownEmptyIframe"></a>handlesKnownEmptyIframe</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="characterReaderBuffer"></a>characterReaderBuffer</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testTemplateInsideTable"></a>testTemplateInsideTable</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="cdataNodesAreTextNodes"></a>cdataNodesAreTextNodes</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="ignoresContentAfterFrameset"></a>ignoresContentAfterFrameset</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesBaseTags"></a>handlesBaseTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testSpanContents"></a>testSpanContents</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handles0CharacterAsText"></a>handles0CharacterAsText</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesFrames"></a>handlesFrames</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesJavadocFont"></a>handlesJavadocFont</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testReinsertionModeForThCelss"></a>testReinsertionModeForThCelss</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesControlCodeInAttributeName"></a>handlesControlCodeInAttributeName</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesInvalidDoctypes"></a>handlesInvalidDoctypes</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesTagsInTextarea"></a>handlesTagsInTextarea</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesCommentsInTable"></a>handlesCommentsInTable</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesEscapedScript"></a>handlesEscapedScript</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testInvalidTableContents"></a>testInvalidTableContents</td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnknownTags"></a>handlesUnknownTags</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="ignoresDupeEndTrTag"></a>ignoresDupeEndTrTag</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="associatedFormControlsWithDisjointForms"></a>associatedFormControlsWithDisjointForms</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesTbodyTable"></a>handlesTbodyTable</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="normalisedBodyAfterContent"></a>normalisedBodyAfterContent</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesSpanInTbody"></a>handlesSpanInTbody</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedTitleAtEof"></a>handlesUnclosedTitleAtEof</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesSolidusAtAttributeEnd"></a>handlesSolidusAtAttributeEnd</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnknownNamespaceTags"></a>handlesUnknownNamespaceTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesKnownEmptyNoFrames"></a>handlesKnownEmptyNoFrames</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="roundTripsCdata"></a>roundTripsCdata</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesManyChildren"></a>handlesManyChildren</td><td>Success</td><td></td><td>0.025</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesTextArea"></a>handlesTextArea</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="convertsImageToImg"></a>convertsImageToImg</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="preSkipsFirstNewline"></a>preSkipsFirstNewline</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="commentAtEnd"></a>commentAtEnd</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selfClosingOnNonvoidIsError"></a>selfClosingOnNonvoidIsError</td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesNestedImplicitTable"></a>handlesNestedImplicitTable</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesRoughAttributes"></a>parsesRoughAttributes</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testUsingSingleQuotesInQueries"></a>testUsingSingleQuotesInQueries</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedDefinitionLists"></a>handlesUnclosedDefinitionLists</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesWhitespaceInoDocType"></a>handlesWhitespaceInoDocType</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="reconstructFormattingElementsInTable"></a>reconstructFormattingElementsInTable</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selfClosingVoidIsNotAnError"></a>selfClosingVoidIsNotAnError</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testNoImagesInNoScriptInHead"></a>testNoImagesInNoScriptInHead</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesBaseWithoutHref"></a>handlesBaseWithoutHref</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedRawtextAtEof"></a>handlesUnclosedRawtextAtEof</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="tracksLimitedErrorsWhenRequested"></a>tracksLimitedErrorsWhenRequested</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="doesNotFindShortestMatchingEntity"></a>doesNotFindShortestMatchingEntity</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="tracksErrorsWhenRequested"></a>tracksErrorsWhenRequested</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedAnchors"></a>handlesUnclosedAnchors</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesBodyFragment"></a>parsesBodyFragment</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnknownInlineTags"></a>handlesUnknownInlineTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testSelectWithOption"></a>testSelectWithOption</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="normalisesEmptyDocument"></a>normalisesEmptyDocument</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="relaxedBaseEntityMatchAndStrictExtendedMatch"></a>relaxedBaseEntityMatchAndStrictExtendedMatch</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesUnclosedFormattingElements"></a>handlesUnclosedFormattingElements</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesSolidusInA"></a>handlesSolidusInA</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testHgroup"></a>testHgroup</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="discardsNakedTds"></a>discardsNakedTds</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesComments"></a>parsesComments</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testFontFlowContents"></a>testFontFlowContents</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesMisnestedTagsBI"></a>handlesMisnestedTagsBI</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesMisnestedTagsBP"></a>handlesMisnestedTagsBP</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="canPreserveBothCase"></a>canPreserveBothCase</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testHandlesDeepSpans"></a>testHandlesDeepSpans</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handleCDataInText"></a>handleCDataInText</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="findsCharsetInMalformedMeta"></a>findsCharsetInMalformedMeta</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesMultiClosingBody"></a>handlesMultiClosingBody</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesBlocksInDefinitions"></a>handlesBlocksInDefinitions</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testAFlowContents"></a>testAFlowContents</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesNullInData"></a>handlesNullInData</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parsesQuiteRoughAttributes"></a>parsesQuiteRoughAttributes</td><td>Success</td><td></td><td>0.000</td>
            </tr>
        </table>
        <div class="Properties">
            <a href="javascript:displayProperties('org.jsoup.parser.HtmlParserTest');">
                    Properties &raquo;
                </a>
        </div>
    </body>
</html>
