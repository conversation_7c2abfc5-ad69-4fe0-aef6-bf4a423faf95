<html xmlns:string="xalan://java.lang.String" xmlns:lxslt="http://xml.apache.org/xslt">
    <head>
        <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <link rel="stylesheet" type="text/css" title="Style" href="../../../stylesheet.css">
    </head>
    <body onload="open('package-frame.html','classListFrame')">
        <h1>Unit Test Results.</h1>
        <table width="100%">
            <tr>
                <td align="left"></td><td align="right">Designed for use with <a href="https://www.junit.org/">JUnit</a> and <a href="https://ant.apache.org/">Ant</a>.</td>
            </tr>
        </table>
        <hr size="1">
        <h3>Package org.jsoup.parser</h3>
        <h2>Classes</h2>
        <p>
            <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
                <tr valign="top">
                    <th width="80%">Name</th><th>Tests</th><th>Errors</th><th>Failures</th><th>Skipped</th><th nowrap>Time(s)</th><th nowrap>Time Stamp</th><th>Host</th>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="14_AttributeParseTest.html">AttributeParseTest</a></td><td><a title="Display all tests" href="14_AttributeParseTest.html">8</a></td><td>0</td><td>0</td><td>0</td><td>0.083</td><td>2025-09-14T06:47:02</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="15_CharacterReaderTest.html">CharacterReaderTest</a></td><td><a title="Display all tests" href="15_CharacterReaderTest.html">23</a></td><td>0</td><td>0</td><td>0</td><td>0.053</td><td>2025-09-14T06:47:03</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="16_HtmlParserTest.html">HtmlParserTest</a></td><td><a title="Display all tests" href="16_HtmlParserTest.html">132</a></td><td>0</td><td>0</td><td>0</td><td>0.333</td><td>2025-09-14T06:47:03</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="17_HtmlTreeBuilderStateTest.html">HtmlTreeBuilderStateTest</a></td><td><a title="Display all tests" href="17_HtmlTreeBuilderStateTest.html">1</a></td><td>0</td><td>0</td><td>0</td><td>0.050</td><td>2025-09-14T06:47:03</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="18_HtmlTreeBuilderTest.html">HtmlTreeBuilderTest</a></td><td><a title="Display all tests" href="18_HtmlTreeBuilderTest.html">1</a></td><td>0</td><td>0</td><td>0</td><td>0.051</td><td>2025-09-14T06:47:03</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="19_ParserSettingsTest.html">ParserSettingsTest</a></td><td><a title="Display all tests" href="19_ParserSettingsTest.html">3</a></td><td>0</td><td>0</td><td>0</td><td>0.053</td><td>2025-09-14T06:47:04</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="20_ParserTest.html">ParserTest</a></td><td><a title="Display all tests" href="20_ParserTest.html">2</a></td><td>0</td><td>0</td><td>0</td><td>0.067</td><td>2025-09-14T06:47:04</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="21_TagTest.html">TagTest</a></td><td><a title="Display all tests" href="21_TagTest.html">10</a></td><td>0</td><td>0</td><td>0</td><td>0.054</td><td>2025-09-14T06:47:04</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="23_TokeniserStateTest.html">TokeniserStateTest</a></td><td><a title="Display all tests" href="23_TokeniserStateTest.html">12</a></td><td>0</td><td>0</td><td>0</td><td>0.091</td><td>2025-09-14T06:47:04</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="24_TokeniserTest.html">TokeniserTest</a></td><td><a title="Display all tests" href="24_TokeniserTest.html">10</a></td><td>0</td><td>0</td><td>0</td><td>0.104</td><td>2025-09-14T06:47:05</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="22_TokenQueueTest.html">TokenQueueTest</a></td><td><a title="Display all tests" href="22_TokenQueueTest.html">9</a></td><td>0</td><td>0</td><td>0</td><td>0.078</td><td>2025-09-14T06:47:04</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
                <tr valign="top" class="Pass">
                    <td><a title="Display all tests" href="25_XmlTreeBuilderTest.html">XmlTreeBuilderTest</a></td><td><a title="Display all tests" href="25_XmlTreeBuilderTest.html">23</a></td><td>0</td><td>0</td><td>0</td><td>0.085</td><td>2025-09-14T06:47:05</td><td>Justins-MacBook-Air-1030.local</td>
                </tr>
            </table>
        </p>
    </body>
</html>
