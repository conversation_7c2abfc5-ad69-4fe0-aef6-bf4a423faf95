<html xmlns:string="xalan://java.lang.String" xmlns:lxslt="http://xml.apache.org/xslt">
    <head>
        <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Unit Test Results: org.jsoup.select.SelectorTest</title>
        <link rel="stylesheet" type="text/css" title="Style" href="../../../stylesheet.css">
        <script type="text/javascript" language="JavaScript">
        var TestCases = new Array();
        var cur;
        
    cur = TestCases['org.jsoup.select.SelectorTest'] = new Array();
    
        cur['ant.core.lib'] = '/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar';
    
        cur['ant.file'] = '/Users/<USER>/Desktop/jsoup/build.xml';
    
        cur['ant.file.jsoup'] = '/Users/<USER>/Desktop/jsoup/build.xml';
    
        cur['ant.file.type'] = 'file';
    
        cur['ant.file.type.jsoup'] = 'file';
    
        cur['ant.home'] = '/opt/homebrew/Cellar/ant/1.10.15_1/libexec';
    
        cur['ant.java.version'] = '23';
    
        cur['ant.library.dir'] = '/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib';
    
        cur['ant.project.default-target'] = 'build';
    
        cur['ant.project.invoked-targets'] = 'clean,test';
    
        cur['ant.project.name'] = 'jsoup';
    
        cur['ant.version'] = 'Apache Ant(TM) version 1.10.15 compiled on August 25 2024';
    
        cur['apple.awt.application.name'] = 'JUnitTestRunner';
    
        cur['basedir'] = '/Users/<USER>/Desktop/jsoup';
    
        cur['debuglevel'] = 'source,lines,vars';
    
        cur['env.__CF_USER_TEXT_ENCODING'] = '0x1F5:0x0:0x0';
    
        cur['env.__CFBundleIdentifier'] = 'com.microsoft.VSCode';
    
        cur['env.COLORTERM'] = 'truecolor';
    
        cur['env.COMMAND_MODE'] = 'unix2003';
    
        cur['env.DISPLAY'] = '/private/tmp/com.apple.launchd.pR1ghfhx8w/org.xquartz:0';
    
        cur['env.GEM_HOME'] = '/Users/<USER>/.gem/ruby/3.4.1';
    
        cur['env.GEM_PATH'] = '/Users/<USER>/.gem/ruby/3.4.1:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0';
    
        cur['env.GEM_ROOT'] = '/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0';
    
        cur['env.GIT_ASKPASS'] = '/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh';
    
        cur['env.GIT_PAGER'] = 'cat';
    
        cur['env.HOME'] = '/Users/<USER>';
    
        cur['env.HOMEBREW_CELLAR'] = '/opt/homebrew/Cellar';
    
        cur['env.HOMEBREW_PREFIX'] = '/opt/homebrew';
    
        cur['env.HOMEBREW_REPOSITORY'] = '/opt/homebrew';
    
        cur['env.INFOPATH'] = '/opt/homebrew/share/info:';
    
        cur['env.JAVA_HOME'] = '/opt/homebrew/opt/openjdk';
    
        cur['env.LANG'] = 'en_US.UTF-8';
    
        cur['env.LESS'] = '-FX';
    
        cur['env.LOGNAME'] = 'justin';
    
        cur['env.MallocNanoZone'] = '0';
    
        cur['env.ORIGINAL_XDG_CURRENT_DESKTOP'] = 'undefined';
    
        cur['env.PAGER'] = 'cat';
    
        cur['env.PATH'] = '/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion Tech Preview.app/Contents/Public:/Users/<USER>/.gem/ruby/3.4.1/bin:/Users/<USER>/.rubies/ruby-3.4.1/lib/ruby/gems/3.4.0/bin:/Users/<USER>/.rubies/ruby-3.4.1/bin:/opt/homebrew/opt/util-linux/sbin:/opt/homebrew/opt/util-linux/bin:/opt/homebrew/opt/llvm/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Python/3.8/bin:/Users/<USER>/Library/Application Support/JetBrains/Toolbox/scripts';
    
        cur['env.PROMPT_COMMAND'] = 'echo "$(pwd)" > \'/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-cwd-4862179404cd48d5.txt\'\n__augment_output_end_marker';
    
        cur['env.PWD'] = '/Users/<USER>/Desktop/jsoup';
    
        cur['env.RUBY_ENGINE'] = 'ruby';
    
        cur['env.RUBY_ROOT'] = '/Users/<USER>/.rubies/ruby-3.4.1';
    
        cur['env.RUBY_VERSION'] = '3.4.1';
    
        cur['env.RUBYOPT'] = '';
    
        cur['env.SCRIPT'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/augment-script-4862179404cd48d5.log';
    
        cur['env.SHELL'] = '/bin/bash';
    
        cur['env.SHLVL'] = '2';
    
        cur['env.SSH_AUTH_SOCK'] = '/private/tmp/com.apple.launchd.65GR7qvoYR/Listeners';
    
        cur['env.TERM'] = 'xterm-256color';
    
        cur['env.TERM_PROGRAM'] = 'vscode';
    
        cur['env.TERM_PROGRAM_VERSION'] = '1.103.2';
    
        cur['env.TMPDIR'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/';
    
        cur['env.USER'] = 'justin';
    
        cur['env.VSCODE_GIT_ASKPASS_EXTRA_ARGS'] = '';
    
        cur['env.VSCODE_GIT_ASKPASS_MAIN'] = '/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js';
    
        cur['env.VSCODE_GIT_ASKPASS_NODE'] = '/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)';
    
        cur['env.VSCODE_GIT_IPC_HANDLE'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/vscode-git-732b350e76.sock';
    
        cur['env.XPC_FLAGS'] = '0x0';
    
        cur['env.XPC_SERVICE_NAME'] = '0';
    
        cur['file.encoding'] = 'UTF-8';
    
        cur['file.separator'] = '/';
    
        cur['ftp.nonProxyHosts'] = 'local|*.local|169.254/16|*.169.254/16';
    
        cur['http.nonProxyHosts'] = 'local|*.local|169.254/16|*.169.254/16';
    
        cur['java.class.path'] = '/Users/<USER>/Desktop/jsoup/ant-target/test-classes:/Users/<USER>/Desktop/jsoup/ant-target/classes:/Users/<USER>/Desktop/jsoup/ant-libs/gson-2.7.jar:/Users/<USER>/Desktop/jsoup/ant-libs/hamcrest-core-1.3.jar:/Users/<USER>/Desktop/jsoup/ant-libs/javax.servlet-api-3.1.0.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-security-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/Desktop/jsoup/ant-libs/junit-4.12.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-launcher.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit.jar:/opt/homebrew/Cellar/ant/1.10.15_1/libexec/lib/ant-junit4.jar';
    
        cur['java.class.version'] = '67.0';
    
        cur['java.home'] = '/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home';
    
        cur['java.io.tmpdir'] = '/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/';
    
        cur['java.library.path'] = '/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.';
    
        cur['java.runtime.name'] = 'OpenJDK Runtime Environment';
    
        cur['java.runtime.version'] = '23.0.2';
    
        cur['java.specification.name'] = 'Java Platform API Specification';
    
        cur['java.specification.vendor'] = 'Oracle Corporation';
    
        cur['java.specification.version'] = '23';
    
        cur['java.vendor'] = 'Homebrew';
    
        cur['java.vendor.url'] = 'https://github.com/Homebrew/homebrew-core/issues';
    
        cur['java.vendor.url.bug'] = 'https://github.com/Homebrew/homebrew-core/issues';
    
        cur['java.vendor.version'] = 'Homebrew';
    
        cur['java.version'] = '23.0.2';
    
        cur['java.version.date'] = '2025-01-21';
    
        cur['java.vm.compressedOopsMode'] = 'Zero based';
    
        cur['java.vm.info'] = 'mixed mode, sharing';
    
        cur['java.vm.name'] = 'OpenJDK 64-Bit Server VM';
    
        cur['java.vm.specification.name'] = 'Java Virtual Machine Specification';
    
        cur['java.vm.specification.vendor'] = 'Oracle Corporation';
    
        cur['java.vm.specification.version'] = '23';
    
        cur['java.vm.vendor'] = 'Homebrew';
    
        cur['java.vm.version'] = '23.0.2';
    
        cur['jdk.debug'] = 'release';
    
        cur['junit.output.dir'] = 'ant-target/reports';
    
        cur['line.separator'] = '\n';
    
        cur['native.encoding'] = 'UTF-8';
    
        cur['os.arch'] = 'aarch64';
    
        cur['os.name'] = 'Mac OS X';
    
        cur['os.version'] = '15.6.1';
    
        cur['path.separator'] = ':';
    
        cur['socksNonProxyHosts'] = 'local|*.local|169.254/16|*.169.254/16';
    
        cur['source'] = '1.8';
    
        cur['stderr.encoding'] = 'UTF-8';
    
        cur['stdout.encoding'] = 'UTF-8';
    
        cur['sun.arch.data.model'] = '64';
    
        cur['sun.boot.library.path'] = '/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib';
    
        cur['sun.cpu.endian'] = 'little';
    
        cur['sun.io.unicode.encoding'] = 'UnicodeBig';
    
        cur['sun.java.command'] = 'org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner org.jsoup.select.SelectorTest skipNonTests=false filtertrace=true haltOnError=false haltOnFailure=false formatter=org.apache.tools.ant.taskdefs.optional.junit.OutErrSummaryJUnitResultFormatter showoutput=false outputtoformatters=true logfailedtests=true threadid=0 logtestlistenerevents=false formatter=org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter,/Users/<USER>/Desktop/jsoup/ant-target/reports/TEST-org.jsoup.select.SelectorTest.xml crashfile=/Users/<USER>/Desktop/jsoup/junitvmwatcher3309209341142844459.properties propsfile=/Users/<USER>/Desktop/jsoup/junit9169315804021469307.properties';
    
        cur['sun.java.launcher'] = 'SUN_STANDARD';
    
        cur['sun.jnu.encoding'] = 'UTF-8';
    
        cur['sun.management.compiler'] = 'HotSpot 64-Bit Tiered Compilers';
    
        cur['target'] = '1.8';
    
        cur['user.country'] = 'US';
    
        cur['user.dir'] = '/Users/<USER>/Desktop/jsoup';
    
        cur['user.home'] = '/Users/<USER>';
    
        cur['user.language'] = 'en';
    
        cur['user.name'] = 'justin';
    </script><script type="text/javascript" language="JavaScript">
        function displayProperties (name) {
          var win = window.open('','JUnitSystemProperties','scrollbars=1,resizable=1');
          var doc = win.document;
          doc.open();
          doc.write("<html><head><title>Properties of " + name + "</title>");
          doc.write("<style type=\"text/css\">");
          doc.write("body {font:normal 68% verdana,arial,helvetica; color:#000000; }");
          doc.write("table tr td, table tr th { font-size: 68%; }");
          doc.write("table.properties { border-collapse:collapse; border-left:solid 1 #cccccc; border-top:solid 1 #cccccc; padding:5px; }");
          doc.write("table.properties th { text-align:left; border-right:solid 1 #cccccc; border-bottom:solid 1 #cccccc; background-color:#eeeeee; }");
          doc.write("table.properties td { font:normal; text-align:left; border-right:solid 1 #cccccc; border-bottom:solid 1 #cccccc; background-color:#fffffff; }");
          doc.write("h3 { margin-bottom: 0.5em; font: bold 115% verdana,arial,helvetica }");
          doc.write("</style>");
          doc.write("</head><body>");
          doc.write("<h3>Properties of " + name + "</h3>");
          doc.write("<div align=\"right\"><a href=\"javascript:window.close();\">Close</a></div>");
          doc.write("<table class='properties'>");
          doc.write("<tr><th>Name</th><th>Value</th></tr>");
          for (prop in TestCases[name]) {
            doc.write("<tr><th>" + prop + "</th><td>" + TestCases[name][prop] + "</td></tr>");
          }
          doc.write("</table>");
          doc.write("</body></html>");
          doc.close();
          win.focus();
        }
      
      </script>
    </head>
    <body>
        <h1>Unit Test Results.</h1>
        <table width="100%">
            <tr>
                <td align="left"></td><td align="right">Designed for use with <a href="https://www.junit.org/">JUnit</a> and <a href="https://ant.apache.org/">Ant</a>.</td>
            </tr>
        </table>
        <hr size="1">
        <h3>Class org.jsoup.select.SelectorTest</h3>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th width="80%">Name</th><th>Tests</th><th>Errors</th><th>Failures</th><th>Skipped</th><th nowrap>Time(s)</th><th nowrap>Time Stamp</th><th>Host</th>
            </tr>
            <tr valign="top" class="Pass">
                <td><a title="Display all tests" href="30_SelectorTest.html">SelectorTest</a></td><td><a title="Display all tests" href="30_SelectorTest.html">62</a></td><td>0</td><td>0</td><td>0</td><td>0.114</td><td>2025-09-14T06:47:06</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
        </table>
        <h2>Tests</h2>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th>Name</th><th>Status</th><th width="80%">Type</th><th nowrap>Time(s)</th>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="containsData"></a>containsData</td><td>Success</td><td></td><td>0.026</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByAttributeStarting"></a>testByAttributeStarting</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="deeperDescendant"></a>deeperDescendant</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selectSameElements"></a>selectSameElements</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoEquals"></a>testPseudoEquals</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByClassCaseInsensitive"></a>testByClassCaseInsensitive</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByAttributeRegexCharacterClass"></a>testByAttributeRegexCharacterClass</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testAllWithClass"></a>testAllWithClass</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByTag"></a>testByTag</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testById"></a>testById</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="findBetweenSpan"></a>findBetweenSpan</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="descendant"></a>descendant</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="handlesCommasInSelector"></a>handlesCommasInSelector</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="notAll"></a>notAll</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByAttribute"></a>testByAttribute</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selectClassWithSpace"></a>selectClassWithSpace</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="notAdjacent"></a>notAdjacent</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testCombinedWithContains"></a>testCombinedWithContains</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="containsWithQuote"></a>containsWithQuote</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoGreaterThan"></a>testPseudoGreaterThan</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testAllElements"></a>testAllElements</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByAttributeRegex"></a>testByAttributeRegex</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="mixCombinator"></a>mixCombinator</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testRelaxedTags"></a>testRelaxedTags</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoBetween"></a>testPseudoBetween</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoLessThan"></a>testPseudoLessThan</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testGroupOr"></a>testGroupOr</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByClass"></a>testByClass</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="and"></a>and</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="attributeWithBrackets"></a>attributeWithBrackets</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPsuedoContainsWithParentheses"></a>testPsuedoContainsWithParentheses</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testGroupOrAttribute"></a>testGroupOrAttribute</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="caseInsensitive"></a>caseInsensitive</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selectFirst"></a>selectFirst</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="containsOwn"></a>containsOwn</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testCharactersInIdAndClass"></a>testCharactersInIdAndClass</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="splitOnBr"></a>splitOnBr</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="multiChildDescent"></a>multiChildDescent</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="adjacentSiblingsWithId"></a>adjacentSiblingsWithId</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testMatches"></a>testMatches</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="matchText"></a>matchText</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selectFirstWithOr"></a>selectFirstWithOr</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="matchTextAttributes"></a>matchTextAttributes</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parentWithClassChild"></a>parentWithClassChild</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="generalSiblings"></a>generalSiblings</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parentChildStar"></a>parentChildStar</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoCombined"></a>testPseudoCombined</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoContains"></a>testPseudoContains</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testPseudoHas"></a>testPseudoHas</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="notClass"></a>notClass</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="notParas"></a>notParas</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selectSupplementaryCharacter"></a>selectSupplementaryCharacter</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="adjacentSiblings"></a>adjacentSiblings</td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testNamespacedTag"></a>testNamespacedTag</td><td>Success</td><td></td><td>0.002</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testNestedHas"></a>testNestedHas</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="parentChildElement"></a>parentChildElement</td><td>Success</td><td></td><td>0.000</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testByAttributeRegexCombined"></a>testByAttributeRegexCombined</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="matchesOwn"></a>matchesOwn</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testSupportsLeadingCombinator"></a>testSupportsLeadingCombinator</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="mixCombinatorGroup"></a>mixCombinatorGroup</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="testWildcardNamespacedTag"></a>testWildcardNamespacedTag</td><td>Success</td><td></td><td>0.001</td>
            </tr>
            <tr valign="top" class="TableRowColor">
                <td><a name="selectFirstWithAnd"></a>selectFirstWithAnd</td><td>Success</td><td></td><td>0.001</td>
            </tr>
        </table>
        <div class="Properties">
            <a href="javascript:displayProperties('org.jsoup.select.SelectorTest');">
                    Properties &raquo;
                </a>
        </div>
    </body>
</html>
