<html xmlns:string="xalan://java.lang.String" xmlns:lxslt="http://xml.apache.org/xslt">
    <head>
        <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Unit Test Results: Summary</title>
        <link rel="stylesheet" type="text/css" title="Style" href="stylesheet.css">
    </head>
    <body onload="open('allclasses-frame.html','classListFrame')">
        <h1>Unit Test Results.</h1>
        <table width="100%">
            <tr>
                <td align="left"></td><td align="right">Designed for use with <a href="https://www.junit.org/">JUnit</a> and <a href="https://ant.apache.org/">Ant</a>.</td>
            </tr>
        </table>
        <hr size="1">
        <h2>Summary</h2>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th>Tests</th><th>Failures</th><th>Errors</th><th>Skipped</th><th>Success rate</th><th>Time</th>
            </tr>
            <tr valign="top" class="Pass">
                <td><a title="Display all tests" href="all-tests.html">658</a></td><td><a title="Display all failures" href="alltests-fails.html">0</a></td><td><a title="Display all errors" href="alltests-errors.html">0</a></td><td><a title="Display all skipped test" href="alltests-skipped.html">0</a></td><td>100.00%</td><td>3.166</td>
            </tr>
        </table>
        <table border="0" width="95%">
            <tr>
                <td style="text-align: justify;">
        Note: <em>failures</em> are anticipated and checked for with assertions while <em>errors</em> are unanticipated.
        </td>
            </tr>
        </table>
        <h2>Packages</h2>
        <table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
            <tr valign="top">
                <th width="80%">Name</th><th>Tests</th><th>Errors</th><th>Failures</th><th>Skipped</th><th nowrap>Time(s)</th><th nowrap>Time Stamp</th><th>Host</th>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/helper/package-summary.html">org.jsoup.helper</a></td><td>44</td><td>0</td><td>0</td><td>0</td><td>0.397</td><td>2025-09-14T06:46:59</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/integration/package-summary.html">org.jsoup.integration</a></td><td>12</td><td>0</td><td>0</td><td>0</td><td>0.177</td><td>2025-09-14T06:47:00</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/internal/package-summary.html">org.jsoup.internal</a></td><td>9</td><td>0</td><td>0</td><td>0</td><td>0.089</td><td>2025-09-14T06:47:00</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/nodes/package-summary.html">org.jsoup.nodes</a></td><td>203</td><td>0</td><td>0</td><td>0</td><td>0.855</td><td>2025-09-14T06:47:00</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/parser/package-summary.html">org.jsoup.parser</a></td><td>234</td><td>0</td><td>0</td><td>0</td><td>1.102</td><td>2025-09-14T06:47:02</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/safety/package-summary.html">org.jsoup.safety</a></td><td>34</td><td>0</td><td>0</td><td>0</td><td>0.092</td><td>2025-09-14T06:47:05</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
            <tr valign="top" class="Pass">
                <td><a href="./org/jsoup/select/package-summary.html">org.jsoup.select</a></td><td>122</td><td>0</td><td>0</td><td>0</td><td>0.454</td><td>2025-09-14T06:47:05</td><td>Justins-MacBook-Air-1030.local</td>
            </tr>
        </table>
    </body>
</html>
