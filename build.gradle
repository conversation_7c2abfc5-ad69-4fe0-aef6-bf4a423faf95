apply plugin: 'java'

description = 'tmurali2'

repositories {
    maven {
        url 'https://repo.maven.apache.org/maven2'
    }
}

// Set custom output directory to gradle-target
buildDir = 'gradle-target'

// Configure Java compilation
java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

// Configure compilation encoding
compileJava {
    options.encoding = 'UTF-8'
}

compileTestJava {
    options.encoding = 'UTF-8'
}

dependencies {
    testImplementation 'junit:junit:4.12'
    testImplementation 'com.google.code.gson:gson:2.7'
    testImplementation 'org.eclipse.jetty:jetty-server:9.2.28.v20190418'
    testImplementation 'org.eclipse.jetty:jetty-servlet:9.2.28.v20190418'
}

// Configure test task to generate HTML reports
test {
    useJUnit()

    // Generate HTML test reports
    reports {
        html.required = true
        html.outputLocation = file("${buildDir}/reports/tests/test")
    }

    // Ensure test resources are available
    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
    }
}
