<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class org.jsoup.helper.DataUtilTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class org.jsoup.helper.DataUtilTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/org.jsoup.helper.html">org.jsoup.helper</a> &gt; DataUtilTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">15</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.047s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">discardsSpuriousByteOrderMark</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">discardsSpuriousByteOrderMarkWhenNoCharsetSet</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">firstMetaElementWithCharsetShouldBeUsedForDecoding</td>
<td class="success">0.029s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generatesMimeBoundaries</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">secondMetaElementWithContentTypeContainsCharsetParameter</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldCorrectCharsetForDuplicateCharsetString</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldNotThrowExceptionOnEmptyCharset</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldReturnNullForIllegalCharsetNames</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldSelectFirstCharsetOnWeirdMultileCharsetsInMetaTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">supportsBOMinFiles</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">supportsUTF8BOM</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">supportsXmlCharsetDeclaration</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCharset</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testQuotedCharset</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">wrongMetaCharsetFallback</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
