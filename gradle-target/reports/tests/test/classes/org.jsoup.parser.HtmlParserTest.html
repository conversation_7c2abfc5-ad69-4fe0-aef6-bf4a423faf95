<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class org.jsoup.parser.HtmlParserTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class org.jsoup.parser.HtmlParserTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/org.jsoup.parser.html">org.jsoup.parser</a> &gt; HtmlParserTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">132</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.199s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">associatedFormControlsWithDisjointForms</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">canPreserveAttributeCase</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">canPreserveBothCase</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">canPreserveTagCase</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">caseInsensitiveParseTree</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">caseSensitiveParseTree</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">cdataNodesAreTextNodes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">characterReaderBuffer</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">commentAtEnd</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">commentBeforeHtml</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">convertsImageToImg</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">createsDocumentStructure</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">createsFormElements</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">createsStructureFromBodySnippet</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">discardsNakedTds</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">doesNotCreateImplicitLists</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">doesNotFindShortestMatchingEntity</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">dropsDuplicateAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">dropsUnterminatedAttribute</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">dropsUnterminatedTag</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">emptyTdTag</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">fallbackToUtfIfCantEncode</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">findsCharsetInMalformedMeta</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handleCDataInText</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handleNullContextInParseFragment</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handles0CharacterAsText</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesBaseTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesBaseWithoutHref</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesBlocksInDefinitions</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesCdata</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesCdataAcrossBuffer</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesCdataInScript</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesCommentsInTable</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesControlCodeInAttributeName</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesDataOnlyTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesDeepStack</td>
<td class="success">0.150s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesEscapedData</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesEscapedScript</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesFrames</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesImplicitCaptionClose</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesInputInTable</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesInvalidDoctypes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesInvalidStartTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesJavadocFont</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesKnownEmptyBlocks</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesKnownEmptyIframe</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesKnownEmptyNoFrames</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesKnownEmptyStyle</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesKnownEmptyTitle</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesManyChildren</td>
<td class="success">0.016s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesMisnestedTagsBI</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesMisnestedTagsBP</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesMultiClosingBody</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesNestedImplicitTable</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesNewlinesAndWhitespaceInTag</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesNullInComments</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesNullInData</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesProtocolRelativeUrl</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesQuotesInCommentsInScripts</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesSolidusAtAttributeEnd</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesSolidusInA</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesSpanInTbody</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesTagsInTextarea</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesTbodyTable</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesTextAfterData</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesTextArea</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedAnchors</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedCdataAtEOF</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedDefinitionLists</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedFormattingElements</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedRawtextAtEof</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedScriptAtEof</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedTitle</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnclosedTitleAtEof</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnexpectedMarkupInTables</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnknownInlineTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnknownNamespaceTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesUnknownTags</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesWhatWgExpensesTableExample</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesWhitespaceInoDocType</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesXmlDeclAndCommentsBeforeDoctype</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesXmlDeclarationAsBogusComment</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">ignoresContentAfterFrameset</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">ignoresDupeEndTrTag</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">noErrorsByDefault</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">noImplicitFormForTextAreas</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">noTableDirectInTable</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalisedBodyAfterContent</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalisesDocument</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalisesEmptyDocument</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalisesHeadlessBody</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalizesDiscordantTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesBodyFragment</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesComments</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesQuiteRoughAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesRoughAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesSimpleDocument</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesUnterminatedComments</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesUnterminatedOption</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parsesUnterminatedTextarea</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">preSkipsFirstNewline</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">preservedCaseLinksCantNest</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">preservesSpaceInScript</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">preservesSpaceInTextArea</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">reconstructFormattingElements</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">reconstructFormattingElementsInTable</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">relaxedBaseEntityMatchAndStrictExtendedMatch</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">retainsAttributesOfDifferentCaseIfSensitive</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">roundTripsCdata</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selfClosingOnNonvoidIsError</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selfClosingVoidIsNotAnError</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAFlowContents</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFontFlowContents</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testFragment</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testHandlesDeepSpans</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testHeaderContents</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testHgroup</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testHtmlLowerCase</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidTableContents</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testNoImagesInNoScriptInHead</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testNormalisesIsIndex</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testReinsertionModeForThCelss</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRelaxedTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSelectWithOption</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSpaceAfterTag</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSpanContents</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSupportsNonAsciiTags</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSupportsPartiallyNonAsciiTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testTemplateInsideTable</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testUsingSingleQuotesInQueries</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">tracksErrorsWhenRequested</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">tracksLimitedErrorsWhenRequested</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
