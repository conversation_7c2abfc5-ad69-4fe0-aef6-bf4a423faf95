<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class org.jsoup.safety.CleanerTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class org.jsoup.safety.CleanerTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/org.jsoup.safety.html">org.jsoup.safety</a> &gt; CleanerTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">34</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.007s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">addsTagOnAttributesIfNotSet</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">bailsIfRemovingProtocolThatsNotSet</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">basicBehaviourTest</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">basicWithImagesTest</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">cleansInternationalText</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">dropsUnresolvableRelativeLinks</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesAllPseudoTag</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesAttributesWithNoValue</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesControlCharactersAfterTagName</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesCustomProtocols</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesFramesets</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesNoHrefAttribute</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">preservesRelativeLinksIfConfigured</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">resolvesRelativeLinks</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">simpleBehaviourTest</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">simpleBehaviourTest2</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">supplyOutputSettings</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCleanAnchorProtocol</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCleanJavascriptHref</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDropComments</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDropImageScript</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDropScript</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDropXmlProc</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDropsUnknownTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testHandlesEmptyAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsValidBodyHtml</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testIsValidDocument</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRelaxed</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRemoveAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRemoveEnforcedAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRemoveProtocols</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRemoveTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testScriptTagInWhiteList</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">whitelistedProtocolShouldBeRetained</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
