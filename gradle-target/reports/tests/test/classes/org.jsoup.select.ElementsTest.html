<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class org.jsoup.select.ElementsTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class org.jsoup.select.ElementsTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/org.jsoup.select.html">org.jsoup.select</a> &gt; ElementsTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">34</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.004s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">absAttr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">after</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">attr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">attributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">before</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">classWithHyphen</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">classes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">eachAttr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">eachText</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">empty</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">eq</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">filter</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">forms</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">hasAbsAttr</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">hasAttr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">hasClassCaseInsensitive</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">hasText</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">html</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">is</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">not</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">outerHtml</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parents</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">remove</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">setHtml</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">siblings</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">tagNameSet</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">text</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">traverse</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">unwrap</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">unwrapKeepsSpace</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">unwrapP</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">val</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">wrap</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">wrapDiv</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
