<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class org.jsoup.select.SelectorTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class org.jsoup.select.SelectorTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/org.jsoup.select.html">org.jsoup.select</a> &gt; SelectorTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">62</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.010s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">adjacentSiblings</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">adjacentSiblingsWithId</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">and</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">attributeWithBrackets</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">caseInsensitive</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">containsData</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">containsOwn</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">containsWithQuote</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">deeperDescendant</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">descendant</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">findBetweenSpan</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generalSiblings</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">handlesCommasInSelector</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">matchText</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">matchTextAttributes</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">matchesOwn</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">mixCombinator</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">mixCombinatorGroup</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">multiChildDescent</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">notAdjacent</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">notAll</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">notClass</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">notParas</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parentChildElement</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parentChildStar</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">parentWithClassChild</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selectClassWithSpace</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selectFirst</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selectFirstWithAnd</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selectFirstWithOr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selectSameElements</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">selectSupplementaryCharacter</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">splitOnBr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAllElements</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testAllWithClass</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByAttribute</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByAttributeRegex</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByAttributeRegexCharacterClass</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByAttributeRegexCombined</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByAttributeStarting</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByClass</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByClassCaseInsensitive</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testById</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testByTag</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCharactersInIdAndClass</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCombinedWithContains</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGroupOr</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGroupOrAttribute</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testMatches</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testNamespacedTag</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testNestedHas</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoBetween</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoCombined</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoContains</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoEquals</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoGreaterThan</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoHas</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPseudoLessThan</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testPsuedoContainsWithParentheses</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testRelaxedTags</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSupportsLeadingCombinator</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testWildcardNamespacedTag</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
