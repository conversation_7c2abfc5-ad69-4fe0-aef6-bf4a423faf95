<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">658</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.507s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/org.jsoup.helper.html">org.jsoup.helper</a>
</td>
<td>44</td>
<td>0</td>
<td>0</td>
<td>0.157s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/org.jsoup.integration.html">org.jsoup.integration</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.043s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/org.jsoup.internal.html">org.jsoup.internal</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/org.jsoup.nodes.html">org.jsoup.nodes</a>
</td>
<td>203</td>
<td>0</td>
<td>0</td>
<td>0.042s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/org.jsoup.parser.html">org.jsoup.parser</a>
</td>
<td>234</td>
<td>0</td>
<td>0</td>
<td>0.223s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/org.jsoup.safety.html">org.jsoup.safety</a>
</td>
<td>34</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/org.jsoup.select.html">org.jsoup.select</a>
</td>
<td>122</td>
<td>0</td>
<td>0</td>
<td>0.033s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/org.jsoup.helper.DataUtilTest.html">org.jsoup.helper.DataUtilTest</a>
</td>
<td>15</td>
<td>0</td>
<td>0</td>
<td>0.047s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.helper.HttpConnectionTest.html">org.jsoup.helper.HttpConnectionTest</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.006s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.helper.W3CDomTest.html">org.jsoup.helper.W3CDomTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.104s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.integration.ParseTest.html">org.jsoup.integration.ParseTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.043s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.internal.StringUtilTest.html">org.jsoup.internal.StringUtilTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.AttributeTest.html">org.jsoup.nodes.AttributeTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.AttributesTest.html">org.jsoup.nodes.AttributesTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.DocumentTest.html">org.jsoup.nodes.DocumentTest</a>
</td>
<td>28</td>
<td>0</td>
<td>0</td>
<td>0.011s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.DocumentTypeTest.html">org.jsoup.nodes.DocumentTypeTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.ElementTest.html">org.jsoup.nodes.ElementTest</a>
</td>
<td>99</td>
<td>0</td>
<td>0</td>
<td>0.020s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.EntitiesTest.html">org.jsoup.nodes.EntitiesTest</a>
</td>
<td>15</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.FormElementTest.html">org.jsoup.nodes.FormElementTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.NodeTest.html">org.jsoup.nodes.NodeTest</a>
</td>
<td>25</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.nodes.TextNodeTest.html">org.jsoup.nodes.TextNodeTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.AttributeParseTest.html">org.jsoup.parser.AttributeParseTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.CharacterReaderTest.html">org.jsoup.parser.CharacterReaderTest</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.HtmlParserTest.html">org.jsoup.parser.HtmlParserTest</a>
</td>
<td>132</td>
<td>0</td>
<td>0</td>
<td>0.199s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.HtmlTreeBuilderStateTest.html">org.jsoup.parser.HtmlTreeBuilderStateTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.HtmlTreeBuilderTest.html">org.jsoup.parser.HtmlTreeBuilderTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.ParserSettingsTest.html">org.jsoup.parser.ParserSettingsTest</a>
</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.ParserTest.html">org.jsoup.parser.ParserTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.TagTest.html">org.jsoup.parser.TagTest</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.TokenQueueTest.html">org.jsoup.parser.TokenQueueTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.TokeniserStateTest.html">org.jsoup.parser.TokeniserStateTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.006s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.TokeniserTest.html">org.jsoup.parser.TokeniserTest</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.009s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.parser.XmlTreeBuilderTest.html">org.jsoup.parser.XmlTreeBuilderTest</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.safety.CleanerTest.html">org.jsoup.safety.CleanerTest</a>
</td>
<td>34</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.select.CssTest.html">org.jsoup.select.CssTest</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>0.018s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.select.ElementsTest.html">org.jsoup.select.ElementsTest</a>
</td>
<td>34</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.select.QueryParserTest.html">org.jsoup.select.QueryParserTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.select.SelectorTest.html">org.jsoup.select.SelectorTest</a>
</td>
<td>62</td>
<td>0</td>
<td>0</td>
<td>0.010s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/org.jsoup.select.TraversorTest.html">org.jsoup.select.TraversorTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
