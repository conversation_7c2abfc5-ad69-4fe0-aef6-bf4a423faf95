<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package org.jsoup.parser</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package org.jsoup.parser</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; org.jsoup.parser</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">234</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.223s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.AttributeParseTest.html">AttributeParseTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.CharacterReaderTest.html">CharacterReaderTest</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.HtmlParserTest.html">HtmlParserTest</a>
</td>
<td>132</td>
<td>0</td>
<td>0</td>
<td>0.199s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.HtmlTreeBuilderStateTest.html">HtmlTreeBuilderStateTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.HtmlTreeBuilderTest.html">HtmlTreeBuilderTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.ParserSettingsTest.html">ParserSettingsTest</a>
</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.ParserTest.html">ParserTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.TagTest.html">TagTest</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.TokenQueueTest.html">TokenQueueTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.TokeniserStateTest.html">TokeniserStateTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.006s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.TokeniserTest.html">TokeniserTest</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.009s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/org.jsoup.parser.XmlTreeBuilderTest.html">XmlTreeBuilderTest</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at Sep 13, 2025, 11:47:47 PM</p>
</div>
</div>
</body>
</html>
