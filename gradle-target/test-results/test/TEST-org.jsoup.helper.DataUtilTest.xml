<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.helper.DataUtilTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.422Z" hostname="<PERSON><PERSON>-<PERSON>B<PERSON>-Air-1030.local" time="0.051">
  <properties/>
  <testcase name="firstMetaElementWithCharsetShouldBeUsedForDecoding" classname="org.jsoup.helper.DataUtilTest" time="0.029"/>
  <testcase name="secondMetaElementWithContentTypeContainsCharsetParameter" classname="org.jsoup.helper.DataUtilTest" time="0.007"/>
  <testcase name="supportsUTF8BOM" classname="org.jsoup.helper.DataUtilTest" time="0.002"/>
  <testcase name="generatesMimeBoundaries" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="discardsSpuriousByteOrderMarkWhenNoCharsetSet" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="discardsSpuriousByteOrderMark" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="shouldCorrectCharsetForDuplicateCharsetString" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="shouldNotThrowExceptionOnEmptyCharset" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="testCharset" classname="org.jsoup.helper.DataUtilTest" time="0.002"/>
  <testcase name="supportsBOMinFiles" classname="org.jsoup.helper.DataUtilTest" time="0.004"/>
  <testcase name="testQuotedCharset" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="wrongMetaCharsetFallback" classname="org.jsoup.helper.DataUtilTest" time="0.001"/>
  <testcase name="shouldSelectFirstCharsetOnWeirdMultileCharsetsInMetaTags" classname="org.jsoup.helper.DataUtilTest" time="0.0"/>
  <testcase name="shouldReturnNullForIllegalCharsetNames" classname="org.jsoup.helper.DataUtilTest" time="0.001"/>
  <testcase name="supportsXmlCharsetDeclaration" classname="org.jsoup.helper.DataUtilTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
