<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.helper.W3CDomTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.486Z" hostname="<PERSON><PERSON>-<PERSON>Book-Air-1030.local" time="0.105">
  <properties/>
  <testcase name="simpleConversion" classname="org.jsoup.helper.W3CDomTest" time="0.036"/>
  <testcase name="namespacePreservation" classname="org.jsoup.helper.W3CDomTest" time="0.002"/>
  <testcase name="convertsGoogleLocation" classname="org.jsoup.helper.W3CDomTest" time="0.038"/>
  <testcase name="treatsUndeclaredNamespaceAsLocalName" classname="org.jsoup.helper.W3CDomTest" time="0.003"/>
  <testcase name="handlesInvalidAttributeNames" classname="org.jsoup.helper.W3CDomTest" time="0.001"/>
  <testcase name="convertsGoogle" classname="org.jsoup.helper.W3CDomTest" time="0.024"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
