<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.integration.ParseTest" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.592Z" hostname="Justin<PERSON>-<PERSON><PERSON><PERSON>-Air-1030.local" time="0.045">
  <properties/>
  <testcase name="testYahooJp" classname="org.jsoup.integration.ParseTest" time="0.007"/>
  <testcase name="testBaiduVariant" classname="org.jsoup.integration.ParseTest" time="0.002"/>
  <testcase name="testGoogleSearchIpod" classname="org.jsoup.integration.ParseTest" time="0.006"/>
  <testcase name="testBaidu" classname="org.jsoup.integration.ParseTest" time="0.002"/>
  <testcase name="testBinaryThrowsException" classname="org.jsoup.integration.ParseTest" time="0.001"/>
  <testcase name="testSmhBizArticle" classname="org.jsoup.integration.ParseTest" time="0.005"/>
  <testcase name="testYahooArticle" classname="org.jsoup.integration.ParseTest" time="0.006"/>
  <testcase name="testBrokenHtml5CharsetWithASingleDoubleQuote" classname="org.jsoup.integration.ParseTest" time="0.001"/>
  <testcase name="testNytArticle" classname="org.jsoup.integration.ParseTest" time="0.004"/>
  <testcase name="testLowercaseUtf8Charset" classname="org.jsoup.integration.ParseTest" time="0.0"/>
  <testcase name="testNewsHomepage" classname="org.jsoup.integration.ParseTest" time="0.008"/>
  <testcase name="testHtml5Charset" classname="org.jsoup.integration.ParseTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
