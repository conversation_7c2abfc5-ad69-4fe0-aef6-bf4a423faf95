<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.internal.StringUtilTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.638Z" hostname="Justin<PERSON>-<PERSON>Book-Air-1030.local" time="0.003">
  <properties/>
  <testcase name="isWhitespace" classname="org.jsoup.internal.StringUtilTest" time="0.001"/>
  <testcase name="padding" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <testcase name="join" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <testcase name="paddingInACan" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <testcase name="normaliseWhiteSpaceHandlesHighSurrogates" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <testcase name="isNumeric" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <testcase name="resolvesRelativeUrls" classname="org.jsoup.internal.StringUtilTest" time="0.001"/>
  <testcase name="normaliseWhiteSpace" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <testcase name="isBlank" classname="org.jsoup.internal.StringUtilTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
