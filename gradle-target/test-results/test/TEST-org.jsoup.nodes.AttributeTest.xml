<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.nodes.AttributeTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.642Z" hostname="<PERSON><PERSON>-<PERSON>B<PERSON>-Air-1030.local" time="0.001">
  <properties/>
  <testcase name="settersOnOrphanAttribute" classname="org.jsoup.nodes.AttributeTest" time="0.0"/>
  <testcase name="testWithSupplementaryCharacterInAttributeKeyAndValue" classname="org.jsoup.nodes.AttributeTest" time="0.001"/>
  <testcase name="html" classname="org.jsoup.nodes.AttributeTest" time="0.0"/>
  <testcase name="booleanAttributesAreEmptyStringValues" classname="org.jsoup.nodes.AttributeTest" time="0.0"/>
  <testcase name="validatesKeysNotEmpty" classname="org.jsoup.nodes.AttributeTest" time="0.0"/>
  <testcase name="validatesKeysNotEmptyViaSet" classname="org.jsoup.nodes.AttributeTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
