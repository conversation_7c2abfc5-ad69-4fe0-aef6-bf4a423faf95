<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.nodes.DocumentTypeTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.660Z" hostname="<PERSON><PERSON>-<PERSON>Book-Air-1030.local" time="0.0">
  <properties/>
  <testcase name="outerHtmlGeneration" classname="org.jsoup.nodes.DocumentTypeTest" time="0.0"/>
  <testcase name="constructorValidationOkWithBlankName" classname="org.jsoup.nodes.DocumentTypeTest" time="0.0"/>
  <testcase name="constructorValidationOkWithBlankPublicAndSystemIds" classname="org.jsoup.nodes.DocumentTypeTest" time="0.0"/>
  <testcase name="testRoundTrip" classname="org.jsoup.nodes.DocumentTypeTest" time="0.0"/>
  <testcase name="constructorValidationThrowsExceptionOnNulls" classname="org.jsoup.nodes.DocumentTypeTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
