<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.nodes.ElementTest" tests="99" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.665Z" hostname="Justin<PERSON>-<PERSON>B<PERSON>-Air-1030.local" time="0.023">
  <properties/>
  <testcase name="testFormatOutline" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="parentlessToString" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="insertChildrenArgumentValidation" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testPrependElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testOuterHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHashcodeIsStableWithContentChanges" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="voidTestFilterCallReturnsElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="elementByTagName" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClonesClassnames" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="appendMustCorrectlyMoveChildrenInsideOneParentElement" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="before" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemoveBeforeIndex" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testChainedRemoveAttributes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="booleanAttributeOutput" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testAddNewHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddNewText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testPreviousElementSiblings" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementById" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testClone" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHtmlContainsOuter" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClassDomMethods" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRoot" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testWrap" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNamespacedElements" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClassNames" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testElementSiblingIndex" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testIs" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testEmptyElementFormatHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testLoopedRemoveAttributes" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testShadowElementsAreUpdated" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testKeepsPreTextAtDepth" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithAttribute" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testNoIndentOnScriptAndStyle" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAppendRowToTable" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithClass" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testShallowClone" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testWrapWithRemainder" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClearAttributes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testKeepsPreTextInCode" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHasClassDomMethods" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetParents" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="getElementsByTagName" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testFormatHtml" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testGetElementsWithAttributeValue" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testWholeText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithAttributeDash" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testManipulateTextNodes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testChildThrowsIndexOutOfBoundsOnMissing" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testPrependRowToTable" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetTextNodes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="after" classname="org.jsoup.nodes.ElementTest" time="0.005"/>
  <testcase name="testContainerOutput" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testInnerHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHasText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemoveAttr" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="textHasSpaceBetweenDivAndCenterTags" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetDataNodes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testCssPath" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testSetHtmlTitle" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testChildrenElements" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="moveByAppend" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testNormalisesText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClassUpdates" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddNewElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHashAndEqualsAndValue" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testElementSiblingIndexSameContent" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetChildText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRelativeUrls" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testSetIndent" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testThrowsOnAddNullText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetSiblingsWithDuplicateContent" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNormalizesInvisiblesInText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="insertChildrenAsCopy" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testTagNameSet" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="dataset" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testPrependText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNormalizesNbspInText" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testRemoveAfterIndex" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testThrowsOnPrependNullText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetSiblings" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testSetHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testSetText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNextElementSiblings" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddBooleanAttribute" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="textHasSpaceAfterBlockTags" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="whiteSpaceClassElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="classNamesAndAttributeNameIsCaseInsensitive" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testPrependNewHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemovingEmptyClassAttributeWhenLastClassRemoved" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testBrHasSpace" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testKeepsPreText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNotPretty" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNextElementSiblingAfterClone" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAppendTo" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="getNamespacedElementsByTag" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testTraverse" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="insertChildrenAtPosition" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="elementIsNotASiblingOfItself" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
