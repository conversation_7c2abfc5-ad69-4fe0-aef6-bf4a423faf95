<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.nodes.EntitiesTest" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.691Z" hostname="Justins-<PERSON>Book-Air-1030.local" time="0.003">
  <properties/>
  <testcase name="strictUnescape" classname="org.jsoup.nodes.EntitiesTest" time="0.001"/>
  <testcase name="escapeSupplementaryCharacter" classname="org.jsoup.nodes.EntitiesTest" time="0.001"/>
  <testcase name="escapesGtInXmlAttributesButNotInHtml" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="escape" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="unescape" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="caseSensitive" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="noSpuriousDecodes" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="notMissingMultis" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="xhtml" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="getByName" classname="org.jsoup.nodes.EntitiesTest" time="0.001"/>
  <testcase name="notMissingSupplementals" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="quoteReplacements" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="escapedSupplementary" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="letterDigitEntities" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <testcase name="unescapeMultiChars" classname="org.jsoup.nodes.EntitiesTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
