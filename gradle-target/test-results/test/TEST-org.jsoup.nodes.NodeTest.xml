<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.nodes.NodeTest" tests="25" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.699Z" hostname="<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Air-1030.local" time="0.006">
  <properties/>
  <testcase name="handlesAbsOnProtocolessAbsoluteUris" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="setBaseUriIsRecursive" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="ownerDocument" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="before" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="handleAbsOnLocalhostFileUris" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="unwrap" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="absHandlesDotFromIndex" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="supportsClone" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="root" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="after" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="absHandlesRelativeQuery" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="unwrapNoChildren" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="literalAbsPrefix" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="handlesBaseUri" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="handlesAbsOnImage" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="nodeIsNotASiblingOfItself" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="handleAbsOnFileUris" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="testReplace" classname="org.jsoup.nodes.NodeTest" time="0.001"/>
  <testcase name="handlesAbsPrefixOnHasAttr" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="childNodesCopy" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="changingAttributeValueShouldReplaceExistingAttributeCaseInsensitive" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="traverse" classname="org.jsoup.nodes.NodeTest" time="0.002"/>
  <testcase name="orphanNodeReturnsNullForSiblingElements" classname="org.jsoup.nodes.NodeTest" time="0.001"/>
  <testcase name="handlesAbsPrefix" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <testcase name="testRemove" classname="org.jsoup.nodes.NodeTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
