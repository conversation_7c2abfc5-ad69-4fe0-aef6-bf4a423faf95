<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.parser.AttributeParseTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.709Z" hostname="Justins-MacBook-Air-1030.local" time="0.001">
  <properties/>
  <testcase name="dropsSlashFromAttributeName" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="moreAttributeUnescapes" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="parsesBooleanAttributes" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="handlesNewLinesAndReturns" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="canStartWithEq" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="parsesRoughAttributeString" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="strictAttributeUnescapes" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <testcase name="parsesEmptyString" classname="org.jsoup.parser.AttributeParseTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
