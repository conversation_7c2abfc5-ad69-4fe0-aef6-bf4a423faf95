<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.parser.HtmlParserTest" tests="132" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.717Z" hostname="Justins-MacBook-Air-1030.local" time="0.203">
  <properties/>
  <testcase name="preservedCaseLinksCantNest" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisesHeadlessBody" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesUnterminatedOption" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesProtocolRelativeUrl" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="dropsUnterminatedTag" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="canPreserveAttributeCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesNullInComments" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesTextAfterData" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="dropsUnterminatedAttribute" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testNormalisesIsIndex" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="noTableDirectInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSupportsPartiallyNonAsciiTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSpaceAfterTag" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesXmlDeclarationAsBogusComment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisesDocument" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="createsStructureFromBodySnippet" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="retainsAttributesOfDifferentCaseIfSensitive" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="canPreserveTagCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="preservesSpaceInScript" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesXmlDeclAndCommentsBeforeDoctype" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testHtmlLowerCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesDeepStack" classname="org.jsoup.parser.HtmlParserTest" time="0.15"/>
  <testcase name="parsesSimpleDocument" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="doesNotCreateImplicitLists" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="parsesUnterminatedTextarea" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testSupportsNonAsciiTags" classname="org.jsoup.parser.HtmlParserTest" time="0.003"/>
  <testcase name="dropsDuplicateAttributes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesWhatWgExpensesTableExample" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesImplicitCaptionClose" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesQuotesInCommentsInScripts" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesInputInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesInvalidStartTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesEscapedData" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="fallbackToUtfIfCantEncode" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="reconstructFormattingElements" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="createsDocumentStructure" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCdataInScript" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedTitle" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="caseInsensitiveParseTree" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handleNullContextInParseFragment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesUnterminatedComments" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="emptyTdTag" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="caseSensitiveParseTree" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testFragment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedScriptAtEof" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesNewlinesAndWhitespaceInTag" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesDataOnlyTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalizesDiscordantTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCdataAcrossBuffer" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="noImplicitFormForTextAreas" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="commentBeforeHtml" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedCdataAtEOF" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesCdata" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyBlocks" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="createsFormElements" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="noErrorsByDefault" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testHeaderContents" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testRelaxedTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyStyle" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyTitle" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="preservesSpaceInTextArea" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnexpectedMarkupInTables" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyIframe" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="characterReaderBuffer" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testTemplateInsideTable" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="cdataNodesAreTextNodes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="ignoresContentAfterFrameset" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesBaseTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSpanContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handles0CharacterAsText" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesFrames" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesJavadocFont" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testReinsertionModeForThCelss" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesControlCodeInAttributeName" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesInvalidDoctypes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesTagsInTextarea" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCommentsInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesEscapedScript" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testInvalidTableContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnknownTags" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="ignoresDupeEndTrTag" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="associatedFormControlsWithDisjointForms" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesTbodyTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisedBodyAfterContent" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesSpanInTbody" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedTitleAtEof" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesSolidusAtAttributeEnd" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnknownNamespaceTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyNoFrames" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="roundTripsCdata" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesManyChildren" classname="org.jsoup.parser.HtmlParserTest" time="0.016"/>
  <testcase name="handlesTextArea" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="convertsImageToImg" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="preSkipsFirstNewline" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="commentAtEnd" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="selfClosingOnNonvoidIsError" classname="org.jsoup.parser.HtmlParserTest" time="0.002"/>
  <testcase name="handlesNestedImplicitTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesRoughAttributes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testUsingSingleQuotesInQueries" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedDefinitionLists" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesWhitespaceInoDocType" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="reconstructFormattingElementsInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="selfClosingVoidIsNotAnError" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testNoImagesInNoScriptInHead" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesBaseWithoutHref" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedRawtextAtEof" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="tracksLimitedErrorsWhenRequested" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="doesNotFindShortestMatchingEntity" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="tracksErrorsWhenRequested" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedAnchors" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="parsesBodyFragment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnknownInlineTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSelectWithOption" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisesEmptyDocument" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="relaxedBaseEntityMatchAndStrictExtendedMatch" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesUnclosedFormattingElements" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesSolidusInA" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testHgroup" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="discardsNakedTds" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesComments" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testFontFlowContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesMisnestedTagsBI" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesMisnestedTagsBP" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="canPreserveBothCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testHandlesDeepSpans" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handleCDataInText" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="findsCharsetInMalformedMeta" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesMultiClosingBody" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesBlocksInDefinitions" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testAFlowContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesNullInData" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesQuiteRoughAttributes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
