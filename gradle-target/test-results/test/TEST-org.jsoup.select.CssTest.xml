<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.select.CssTest" tests="17" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.960Z" hostname="<PERSON><PERSON>-<PERSON>B<PERSON>-Air-1030.local" time="0.018">
  <properties/>
  <testcase name="nthLastOfType_advanced" classname="org.jsoup.select.CssTest" time="0.002"/>
  <testcase name="nthChild_advanced" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="nthOfType_advanced" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="nthOfType_simple" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="firstOfType" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="firstChild" classname="org.jsoup.select.CssTest" time="0.0"/>
  <testcase name="nthLastOfType_simple" classname="org.jsoup.select.CssTest" time="0.002"/>
  <testcase name="root" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="onlyOfType" classname="org.jsoup.select.CssTest" time="0.0"/>
  <testcase name="nthLastChild_simple" classname="org.jsoup.select.CssTest" time="0.002"/>
  <testcase name="empty" classname="org.jsoup.select.CssTest" time="0.0"/>
  <testcase name="onlyChild" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="nthChild_simple" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="nthLastChild_advanced" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="lastOfType" classname="org.jsoup.select.CssTest" time="0.003"/>
  <testcase name="lastChild" classname="org.jsoup.select.CssTest" time="0.001"/>
  <testcase name="nthOfType_unknownTag" classname="org.jsoup.select.CssTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
