<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.select.QueryParserTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:46.984Z" hostname="Justin<PERSON>-<PERSON>Book-Air-1030.local" time="0.0">
  <properties/>
  <testcase name="testParsesSingleQuoteInContains" classname="org.jsoup.select.QueryParserTest" time="0.0"/>
  <testcase name="testOrGetsCorrectPrecedence" classname="org.jsoup.select.QueryParserTest" time="0.0"/>
  <testcase name="testParsesMultiCorrectly" classname="org.jsoup.select.QueryParserTest" time="0.0"/>
  <testcase name="exceptionOnUncloseAttribute" classname="org.jsoup.select.QueryParserTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
