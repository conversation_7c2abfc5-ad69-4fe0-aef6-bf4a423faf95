<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="org.jsoup.select.TraversorTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-09-14T06:47:47Z" hostname="<PERSON><PERSON>-<PERSON>B<PERSON>-Air-1030.local" time="0.001">
  <properties/>
  <testcase name="filterStop" classname="org.jsoup.select.TraversorTest" time="0.0"/>
  <testcase name="filterVisit" classname="org.jsoup.select.TraversorTest" time="0.0"/>
  <testcase name="filterSkipChildren" classname="org.jsoup.select.TraversorTest" time="0.0"/>
  <testcase name="filterSkipEntirely" classname="org.jsoup.select.TraversorTest" time="0.0"/>
  <testcase name="filterRemove" classname="org.jsoup.select.TraversorTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
