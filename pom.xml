<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<name>jsoup Java HTML Parser</name>

	<groupId>org.jsoup</groupId>
	<artifactId>jsoup</artifactId>
	<version>1.12.2-SNAPSHOT</version>


	<build>
		<plugins>
			<!-- Ensure the jdk version for compilation -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<source>8</source>
					<target>8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Checkstyle for code smells and static analysis -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>3.0.0</version>
				<dependencies>
					<dependency>
						<groupId>com.puppycrawl.tools</groupId>
						<artifactId>checkstyle</artifactId>
						<version>8.9</version>
					</dependency>
				</dependencies>
				<configuration>
				  <encoding>UTF-8</encoding>
				  <linkXRef>false</linkXRef>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.openclover</groupId>
				<artifactId>clover-maven-plugin</artifactId>
				<version>4.4.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>


	<dependencies>
		<dependency>
			<!-- junit -->
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<!-- gson, to fetch entities from w3.org -->
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.7</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<!-- jetty for webserver integration tests. 9.2 is last with Java7 support -->
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-server</artifactId>
			<version>9.2.28.v20190418</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<!-- jetty for webserver integration tests -->
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-servlet</artifactId>
			<version>9.2.28.v20190418</version>
			<scope>test</scope>
		</dependency>
	</dependencies>


	<!-- Checkstyle for code smells and static analysis -->
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
				  <encoding>UTF-8</encoding>
				  <linkXRef>false</linkXRef>
				</configuration>
			</plugin>
		</plugins>
	</reporting>

	<properties>
		<checkstyle.config.location>my-stylecheck.xml</checkstyle.config.location>
	</properties>

</project>
