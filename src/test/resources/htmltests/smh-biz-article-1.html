<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
    <title>The board’s next fear: the female quota</title>
    <meta http-equiv="content-language" content="en" />
<meta http-equiv="imagetoolbar" content="no" />
<meta name="robots" content="noarchive,noodp" />
<meta name="robots" content="ACAP allow-index" />
<meta name="robots" content="ACAP allow-follow" />
<meta name="robots" content="ACAP disallow-preserve" />
<meta name="robots" content="ACAP allow-present prohibited-modification=annotation" />
<meta name="description" content="" />
<meta name="keywords" content="" />



<!-- Styles =================================================================== -->
<!-- Have these styles for both screen and print to create a base for printing: -->
<link rel="stylesheet" type="text/css" media="screen,print" href="http://resources.smh.com.au/common/media-common-1.0/css/base.css" />
<!-- Skin CSS: -->
<link rel="stylesheet" type="text/css" media="screen"       href="http://resources.smh.com.au/common/media-common-1.0/css/base-skin-news/skin-news.css" />
<link rel="stylesheet" type="text/css" media="screen"       href="http://resources.smh.com.au/common/media-common-1.0/css/skin-business/skin-business.css" />
    <link rel="stylesheet" type="text/css" media="screen" href="http://resources.smh.com.au/common/media-common-1.0/css/skin-promotions/skin-promotions.css" />
<link rel="alternate stylesheet" type="text/css" media="screen" title="High contrast" href="http://resources.smh.com.au/common/media-common-1.0/css/base-skin-news/skin-news-low-vision.css" />
<!-- Any adjustments we need to do for handheld devices: -->
<link rel="stylesheet" type="text/css" media="handheld"     href="http://resources.smh.com.au/common/media-common-1.0/css/mobile.css" />

<script type="text/javascript" >
    var delayedAds = [];
</script>



    <!-- All Javascript calls go here, after the content so loading and parsing a script does not affect content loading: -->
    <script type="text/javascript" src="http://resources.smh.com.au/common/media-common-1.0/js/fd.mt.media.com.au.js"></script>
<script type="text/javascript" src="http://resources.smh.com.au/common/media-common-1.0/js/fd.media.custom.js"></script>
<script type="text/javascript" src="http://resources.smh.com.au/common/media-common-1.0/js/adParams.js"></script>
<script type="text/javascript" src="http://resources.smh.com.au/common/media-common-1.0/js/europa.lite.packed.js"></script>

<script type="text/javascript">

    function initPreAdLoadImage() {
        new Element("img", {
            width: 1,
            height: 1,
            src: "http://direct.fairfax.com.au/vserver/CCID=1/AREA=BUSINESS.SMH.BUSINESS.COLUMNIST/BT=1/ACC_RANDOM=678700",
            styles: {
                display: 'none'
            }
        });
    }

    FD.register("PreAdLoadImage");

    var siteAdvertDesc = {
        redir: "/adredirect.html?ad="
    };


    FD.baseAd = {
        src: 	"http://direct.fairfax.com.au/jserver/",
        params: {
            cat: "BUSINESS",
            site: "ONL.MH.SMH.BUSINESS",
            ctype: "ARTICLE",
            area: "BUSINESS.SMH.BUSINESS.COLUMNIST",
            cat1: "COLUMNIST",
        isiframe: "yes"
        }
    };

    function initPost() {


        try {
            document.domain = "smh.com.au";
        }
        catch (e)
        {
            // if working dev the above will fail
        }

        for(var i = 0; i < delayedAds.length; i++) {
            delayedAds[i]();
        }
    }
</script>

</head>

<body class="smh business">
<script type="text/javascript">
// <![CDATA[
	document.body.className += " scriptable";
// ]]>
</script>


<a class="skipLink" href="#nav">Skip to navigation</a>
<a class="skipLink" href="#content">Skip to content</a>

<div class="wrap cfix">

    <div id="adspot-1x10" class=""></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-1x10",
            iframeId: "adspot-1x10-iframe",
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "1x10"
        }),getAdParams("1x10"))

            ,height: 1
            ,width: 1
            })
        );
    }
);

</script>


<!-- Network strip top (scope: network-wide) -->
<div class="nN-whiteStrip">
    <a class="logo" href="http://www.fairfaxdigital.com.au/" onclick="linktop(this);">Fairfax Digital</a>
    <ul class="links">
        <li><a href="http://newsbreak.com.au/" onclick="linktop(this);">News</a></li>
        <li><a href="http://mycareer.com.au/" onclick="linktop(this);">MyCareer</a></li>
        <li><a href="http://www.domain.com.au/" onclick="linktop(this);">Domain</a></li>
        <li><a href="http://www.drive.com.au/" onclick="linktop(this);">Drive</a></li>
        <li><a href="http://businessday.com.au/" onclick="linktop(this);">Finance</a></li>
        <li><a href="http://mobile.fairfax.com.au/" onclick="linktop(this);">Mobile</a></li>
        <li><a href="http://www.rsvp.com.au/" onclick="linktop(this);">RSVP</a></li>
        <li><a href="http://www.smh.com.au/travel/travel-landing/" onclick="linktop(this);">Travel</a></li>
        <li class="last"><a href="http://www.weatherzone.com.au/" onclick="linktop(this);">Weather</a></li>
    </ul>
    <ul class="memberCentre">
        <li><a href="http://www.fairfax.com.au/map/" onclick="linktop(this);">network map</a></li><li class="last"><a href="http://fairfaxdigital.com.au/" onclick="linktop(this);">member centre</a></li>
    </ul>
</div>
<!-- End component: Network strip top -->


<!-- Header (class according to new HTML5 element 'header'; was 'masthead') (scope: network-wide) -->
<div class="header span-24">
    <div id="adspot-468X60-pos-1" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-468X60-pos-1",
            iframeId: "adspot-468X60-pos-1-iframe",
            params: $merge($merge(FD.baseAd.params, {
                    pos: 1,
                    adtype: 'panorama',
                aamsz : "468X60"
        }),getAdParams("468X60"))

            })
        );
    }
);

</script>
    <p class="mh-logo"><a href="http://www.smh.com.au" title="The Sydney Morning Herald">The Sydney Morning Herald</a></p>
            <h2><a href="http://business.smh.com.au" title="Business">Business</a></h2>

        <!-- Navigation (scope: network-wide) -->
    <ul id="nav" class="hasSubNav">




                <li class="selected">
                    <a href="http://www.smh.com.au/business" title="News">News</a>

                        <ol>
                                <li>
                                    <a href="http://www.smh.com.au/business/national" title="Today's News & Views">Today's News & Views</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/business/opinion" title="Comment & Analysis">Comment & Analysis</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/business/world" title="World Business">World Business</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/business/marketing" title="Media & Marketing">Media & Marketing</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/business/print-edition" title="Newspaper Edition">Newspaper Edition</a>
                                </li>
                                <li class="last">
                                    <a href="http://mobile.fairfax.com.au/" title="BusinessDay Mobile">BusinessDay Mobile</a>
                                </li>
                        </ol>


                    </li>






                <li>
                    <a href="http://www.smh.com.au/business/markets" title="Markets">Markets</a>



                    </li>






                <li>
                    <a href="http://markets.smh.com.au/apps/qt/index.ac" title="Quotes">Quotes</a>



                    </li>






                <li>
                    <a href="http://markets.smh.com.au/apps/pf/index.ac" title="Portfolio">Portfolio</a>



                    </li>






                <li>
                    <a href="http://www.smh.com.au/business/money/" title="Money">Money</a>



                    </li>






                <li>
                    <a href="http://www.smh.com.au/business/property" title="Property Focus">Property Focus</a>



                    </li>






                <li class=" hasDropdown startup">
                    <a href="http://smallbusiness.smh.com.au/" title="Small Business">Small Business</a>


                        <ul>
                                <li class="first">
                                    <a href="http://www.smh.com.au/small-business/startup" title="Startup">Startup</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/managing" title="Managing">Managing</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/franchising" title="Franchising">Franchising</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/trends" title="Trends">Trends</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/entrepreneur" title="Entrepreneur">Entrepreneur</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/marketing" title="Marketing">Marketing</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/finance" title="Finance">Finance</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/technology" title="Technology">Technology</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/small-business/resources" title="Resources">Resources</a>
                                </li>
                                <li class="last">
                                    <a href="http://www.smh.com.au/small-business/coaching" title="Coaching">Coaching</a>
                                </li>
                        </ul>

                    </li>






                <li class=" hasDropdown ">
                    <a href="http://www.smh.com.au/business/executivestyle/" title="Executive Style">Executive Style</a>


                        <ul>
                                <li class="first">
                                    <a href="http://www.smh.com.au/executive-style/travel/" title="Travel">Travel</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/motors/" title="Motors">Motors</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/culture/" title="Culture">Culture</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/gadgets" title="Gadgets">Gadgets</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/luxury" title="Luxury">Luxury</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/management/" title="Management">Management</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/style/" title="Style">Style</a>
                                </li>
                                <li>
                                    <a href="http://www.smh.com.au/executive-style/top-drop" title="Wine">Wine</a>
                                </li>
                                <li class="last">
                                    <a href="http://www.smh.com.au/executive-style/fitness" title="Fitness">Fitness</a>
                                </li>
                        </ul>

                    </li>






                <li class=" hasDropdown ">
                    <a href="#" title="Compare & Save">Compare & Save</a>


                        <ul>
                                <li class="first">
                                    <a href="http://compare.smh.com.au/credit-cards" title="Credit Cards">Credit Cards</a>
                                </li>
                                <li>
                                    <a href="http://compare.smh.com.au/debit-cards" title="Debit Cards">Debit Cards</a>
                                </li>
                                <li>
                                    <a href="http://compare.smh.com.au/home-loans" title="Home Loans">Home Loans</a>
                                </li>
                                <li>
                                    <a href="http://compare.smh.com.au/personal-loans" title="Personal Loans">Personal Loans</a>
                                </li>
                                <li>
                                    <a href="http://compare.smh.com.au/car-loans" title="Car Loans">Car Loans</a>
                                </li>
                                <li>
                                    <a href="http://compare.smh.com.au/term-deposits" title="Term Deposits">Term Deposits</a>
                                </li>
                                <li>
                                    <a href="http://compare.smh.com.au/bank-accounts" title="Bank Accounts">Bank Accounts</a>
                                </li>
                                <li class="last">
                                    <a href="http://compare.smh.com.au/savings-accounts" title="Saving Accounts">Saving Accounts</a>
                                </li>
                        </ul>

                    </li>






                <li class=" sponsor-1">
                    <a href="http://www.smh.com.au/national/cfdeducation" title="Trader Insights">Trader Insights</a>



                    </li>


    </ul>
    <!-- End module Navigation -->


        <!-- Breadcrumb (scope: network-wide) -->
    <p class="breadcrumb">
        <span>You are here:</span>
            <a href="http://www.smh.com.au">Home</a>
            &raquo; <a href="http://www.smh.com.au/business">BusinessDay</a>
            &raquo; <a href="http://www.smh.com.au/business/by/Michael-Pascoe">Michael Pascoe</a>
            &raquo; <a href="http://www.smh.com.au/business/the-boards-next-fear-the-female-quota-********-lteq.html">Article</a>
    </p>


        <ul class="altFormats">
            <li class="mobiles">
                <a href="http://mobile.fairfax.com.au/">Mobiles</a>
            </li>
            <li class="rss">
                <a href="http://www.smh.com.au/rssheadlines">RSS</a>
            </li>
            <li class="newsletters">
                <a href="https://membercentre.fairfax.com.au/NewsletterSubscription.aspx">Newsletters</a>
            </li>
        <li id="vision" class="last"><a href="#">High contrast</a></li>
    </ul>


        <!-- Search (scope:network-wide) -->
    <div class="cN-searchBox cfix">
    <form action="http://www.smh.com.au/execute_search.html" method="get">
        <h2>Search smh:</h2>
        <label for="search">
            <input type="text" name="text" id="search" value='Search here...' onfocus="this.value='';" />
        </label>
        <h3>Search in:</h3>
        <ul id="ddown" class="ddown">
            <li><a class="selected" href="#">Business</a>
                <div class="srch-wrap">
					<div></div>
                    <ul class="cfix">
                            <li class="first"><a href="#">smh.com.au</a></li>
                            <li><a href="#">Web</a></li>
                            <li><a href="#">Business</a></li>
                    </ul>
                </div>
            </li>
        </ul>
        <input type="hidden" name="ss" value="Business" />
        <input type="submit" class="btnSubmit" value="Search" />
    </form>
    </div>


</div>
<!-- End component: Header -->



	<div id="adspot-940X20-pos-1" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-940X20-pos-1",
            iframeId: "adspot-940X20-pos-1-iframe",
            params: $merge($merge(FD.baseAd.params, {
                    pos: 1,
                    adtype: 'panorama',
                aamsz : "940X20"
        }),getAdParams("940X20"))

            ,height: 1
            ,width: 940
            })
        );
    }
);

</script>

    <!-- Content. We use an id here to be able to jump to this section. -->
    <div id="content" class="span-16">

        <!-- cN-headingPage -->
        <h1 class="cN-headingPage prepend-5 span-11 last"><HEADLINE>The board’s next fear: the female quota</HEADLINE></h1>

        <!-- Class 'push-0' just right-aligns the element so that the main content comes first. -->
        <div class="push-0 span-11 last">


<!-- cT-storyDetails -->
<div class="cT-storyDetails cfix">
        <cite>January 6, 2010 - 1:12PM</cite>
</div>

<div class="ad adSpot-textBox" id="googleAds"></div>

<BOD>
    <div class="articleBody">
            <p>For all the protestations, there’s been an almost audible sigh of relief in the directors’ club about the watered-down and more workable recommendations from the Productivity Commission on remuneration report votes. Besides, the whole question of obscene executive pay was effectively ducked anyway.</p>
            <p>But the way is now cleared for various boardrooms to move on to the next big fear: that the Federal Government might impose a quota for female directors. After all, the Labor Party has done something like that to itself with a quota of 40 per cent of candidates being women, resulting in a third of Labor MHRs being female.</p>
            <p>(In case you’re wondering, women have 27 per cent of all House of Reps seats and make up 36 per cent of the Senate.)</p>
            <p>In a recent social setting and therefore anonymously, a prominent member of the directors’ club and chairman of major company told me his fellows were appalled at the prospect.</p>
            <p>The usual reservations were cited: a quota means the best person for the job may not be chosen; it demeans women who are chosen as the suspicion will be that they didn’t get the job on merit.</p>
            <p>But much more important to this chairman was that there was not a sufficient pool of female talent at the top of the executive aquatic centre from which to fish an imposed quota of directors. And once a government started mandating quotas for one group, he feared it would be liable to repeat the exercise for another. A quota for indigenous Australians, perhaps.</p>
            <p>My suspicion is that this chairman was representative of the thinking of his peers, if not what they’re prepared to say in public. In that case, there’s a lot more to be done in the evolution of women CEOs before the boardrooms can start to mirror Federal Parliament – and it shouldn’t need to be said that there is a vast difference between what’s required to be a “successful” MP and a successful director, as the miserable boardroom careers of many retired pollies indicates.</p>
            <p>This week’s Economist magazine cover revives the World War II image of Rosie the Riveter with the headline: “We Did It! What happens when women are over half the workforce”. It celebrates the fact that in the next few months, women will cross the 50 per cent threshold and make up the majority of the American workforce.</p>
            <p>“Women already make up the majority of university graduates in the OECD countries and the majority of professional workers in several rich countries, including the United States. Women run many of the world’s great companies, from PepsiCo in America to Areva in France,” notes the Economist’s leader – and it could have continued “and Westpac and Harvey Norman in Australia”.</p>
            <p>“Women’s economic empowerment is arguably the biggest social change of our times. Just a generation ago, women were largely confined to repetitive, menial jobs. They were routinely subjected to casual sexism and were expected to abandon their careers when they married and had children. Today they are running some of the organisations that once treated them as second-class citizens. Millions of women have been given more control over their own lives. And millions of brains have been put to more productive use. Societies that try to resist this trend - most notably the Arab countries, but also Japan and some southern European countries - will pay a heavy price in the form of wasted talent and frustrated citizens.”</p>
            <p>The leader goes on to face the two major remaining issues: that women are still under-represented at the top making up only 2 per cent of the bosses of major American companies and 5 per cent of British; and that women are paid significantly less than men on average.</p>
            <p>In keeping with the Economist’s rational and liberal credo, it argues for, mostly, letting the market do the work in fixing those problems as it’s been doing a good job of it over the past decade. When the Scandinavian experience is cited as a way of speeding up the process, the magazine answers:</p>
            <p>“If that means massive intervention, in the shape of affirmative-action programs and across-the-board benefits for parents of all sorts, the answer is no. To begin with, promoting people on the basis of their sex is illiberal and unfair, and stigmatises its beneficiaries. And there are practical problems. Lengthy periods of paid maternity leave can put firms off hiring women, which helps explain why most Swedish women work in the public sector and Sweden has a lower proportion of women in management than America does.”</p>
            <p>The leader argues that there are plenty of cheaper and subtler ways in which governments can make life easier for women.</p>
            <p>“Welfare states were designed when most women stayed at home. They need to change the way they operate. German schools, for instance, close at midday. American schools shut down for two months in the summer. These things can be changed without huge cost.”</p>
            <p>Perhaps without huge cost, but it would be a very brave minister who would take on the Teachers’ Union for a start. It would be so much easier for a federal Labor politicians to simply mandate a female quota for the nation’s boardrooms, but as the saying goes: For every large and difficult problem, you can generally find an easy solution – and it will be wrong.</p>
            <p><span style="font-style: italic;">Michael Pascoe is a BusinessDay contributing editor.</span></p>
    </div>
    <!-- articleBody -->
</BOD>



            <div class="ad adSpot-textBox" id="moreGoogleAds"></div>

            <div align="right">
                <a href="http://direct.fairfax.com.au/adclick/CID=000215760000000000000000 "><b>To advertise your business on Google...click here</b></a>
            </div>

                <!-- cT-comments -->
<div id="comments" class="cT-comments">
    <a id="makeComment" name="makeComment"></a>


</div>
<!-- cT-comments -->


            <div class="adSpot-textBoxGraphicRight cfix">
    <div id="adspot-300x20-pos-1" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-300x20-pos-1",
            iframeId: "adspot-300x20-pos-1-iframe",
                src:[
                        FD.baseAd.src + 'POS=1/',
                        FD.baseAd.src + 'POS=2/'
                ],
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "300x20"
        }),getAdParams("300x20"))

            })
        );
    }
);

</script>
    <div id="adspot-300x20-pos-2" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-300x20-pos-2",
            iframeId: "adspot-300x20-pos-2-iframe",
                src:[
                        FD.baseAd.src + 'POS=3/',
                        FD.baseAd.src + 'POS=4/'
                ],
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "300x20"
        }),getAdParams("300x20"))

            })
        );
    }
);

</script>
</div>


        </div><!-- inner content area -->


        <div class="sidebar span-5">
                <div class="cT-headshot">
        <div>
                <a href="http://www.smh.com.au/business/by/Michael-Pascoe" title="Michael Pascoe"><img src="http://images.smh.com.au/2009/10/24/810836/michael-pascoe_127x127-90x90.jpg" width="90" height="90" alt="michael-pascoe_127x127" /></a>

            <h3><a href="http://www.smh.com.au/business/by/Michael-Pascoe" title="Michael Pascoe">Michael Pascoe</a></h3>
        </div>
        <p>

            <a href="http://www.smh.com.au/business/by/Michael-Pascoe">
                More Michael Pascoe articles
            </a>
        </p>
    </div>

            <div class="cT-socialCommenting accessibleSocialComment">


    <h3>Join the conversation</h3>

        <p class="people"><em>2</em> people are reading this now.</p>
        <p class="twitter"><a href="http://twitter.com/home?status=%23fdlteq http://smh.com.au/business-lteq.html" target="_blank">Comment on Twitter</a>.<br />
        <a href="http://search.twitter.com/search?q=%23fdlteq" target="_blank"><em>Read tweets</em></a>.</p>

</div>









                    <div class="cN-linkList">
        <h3>Top Business articles</h3>
        <ol>
                    <li class="first">
                <a href="http://www.smh.com.au/business/westpac-rate-rise-pushes-customers-to-switch-banks-********-lsbd.html" title="Westpac rate rise 'pushes customers to switch banks'" style="">Westpac rate rise 'pushes customers to switch banks'</a>
                </li>
                    <li>
                <a href="http://www.smh.com.au/business/a-strikes-twoyear-high-against-the-euro-********-ls79.html" title="$A strikes two-year high against the euro" style="">$A strikes two-year high against the euro</a>
                </li>
                    <li>
                <a href="http://www.smh.com.au/business/red-hot-property-sector-points-to-rate-rise-********-ltb6.html" title="'Red hot' property sector points to rate rise" style="">'Red hot' property sector points to rate rise</a>
                </li>
                    <li>
                <a href="http://www.smh.com.au/business/jetstar-and-airasia-in-lowcost-alliance-********-lsze.html" title="Jetstar and AirAsia in low-cost alliance" style="">Jetstar and AirAsia in low-cost alliance</a>
                </li>
                    <li>
                <a href="http://www.smh.com.au/business/rip-out-vineyards-lehmann-********-ls76.html" title="Rip out vineyards: Lehmann" style="">Rip out vineyards: Lehmann</a>
                </li>
                <li class="last"><a href="http://www.smh.com.au/business">More Business articles</a></li>
        </ol>
    </div>


            <div class="cN-topicSelector">
        <h3>Business Topics</h3>
                        <div class="cN-groupNavigator open">
                    <h4><a href="#">Companies<span>Expand</span> (4618)</a></h4>
                    <ul>
                                <li class="all"><a href="/business/Companies">All Companies</a> (4618)</li>
                                <li><a href="/business/Companies/AMP">AMP</a> (827)</li>
                                <li><a href="/business/Companies/Westpac">Westpac</a> (567)</li>
                                <li><a href="/business/Companies/ANZ">ANZ</a> (544)</li>
                                <li><a href="/business/Companies/Commonwealth-Bank">Commonwealth Bank</a> (498)</li>
                                <li><a href="/business/Companies/BHP-Billiton">BHP Billiton</a> (425)</li>
                                    <li class="hide"><a href="/business/Companies/Rio-Tinto">Rio Tinto</a> (417)</li>
                                    <li class="hide"><a href="/business/Companies/NAB">NAB</a> (344)</li>
                                    <li class="hide"><a href="/business/Companies/Telstra">Telstra</a> (343)</li>
                                    <li class="hide"><a href="/business/Companies/Seek">Seek</a> (299)</li>
                                    <li class="hide"><a href="/business/Companies/Woolworths">Woolworths</a> (255)</li>
                                    <li class="hide"><a href="/business/Companies/Qantas">Qantas</a> (223)</li>
                                    <li class="hide"><a href="/business/Companies/Wesfarmers">Wesfarmers</a> (183)</li>
                                    <li class="hide"><a href="/business/Companies/David-Jones">David Jones</a> (156)</li>
                                    <li class="hide"><a href="/business/Companies/AXA">AXA</a> (149)</li>
                                    <li class="hide"><a href="/business/Companies/Woodside-Petroleum">Woodside Petroleum</a> (141)</li>
                                    <li class="hide"><a href="/business/Companies/Santos">Santos</a> (128)</li>
                                    <li class="hide"><a href="/business/Companies/ING">ING</a> (124)</li>
                                    <li class="hide"><a href="/business/Companies/Fortescue-Metals">Fortescue Metals</a> (105)</li>
                                    <li class="hide"><a href="/business/Companies/Fairfax-Media">Fairfax Media</a> (102)</li>
                                    <li class="hide"><a href="/business/Companies/Oil-Search">Oil Search</a> (96)</li>
                                    <li class="hide"><a href="/business/Companies/Origin-Energy">Origin Energy</a> (96)</li>
                                    <li class="hide"><a href="/business/Companies/Macquarie-Airports">Macquarie Airports</a> (91)</li>
                                    <li class="hide"><a href="/business/Companies/Leighton">Leighton</a> (91)</li>
                                    <li class="hide"><a href="/business/Companies/Crown">Crown</a> (87)</li>
                                    <li class="hide"><a href="/business/Companies/AWB">AWB</a> (87)</li>
                                    <li class="hide"><a href="/business/Companies/Centro">Centro</a> (82)</li>
                                    <li class="hide"><a href="/business/Companies/Lend-Lease">Lend Lease</a> (81)</li>
                                    <li class="hide"><a href="/business/Companies/ERA">ERA</a> (80)</li>
                                    <li class="hide"><a href="/business/Companies/Perpetual">Perpetual</a> (75)</li>
                                    <li class="hide"><a href="/business/Companies/Seven-Network">Seven Network</a> (74)</li>
                                    <li class="hide"><a href="/business/Companies/Virgin-Blue">Virgin Blue</a> (73)</li>
                                    <li class="hide"><a href="/business/Companies/Bank-of-Queensland">Bank of Queensland</a> (71)</li>
                                    <li class="hide"><a href="/business/Companies/Harvey-Norman">Harvey Norman</a> (69)</li>
                                    <li class="hide"><a href="/business/Companies/Westfield">Westfield</a> (68)</li>
                                    <li class="hide"><a href="/business/Companies/OZ-Minerals">OZ Minerals</a> (66)</li>
                                    <li class="hide"><a href="/business/Companies/Bunnings">Bunnings</a> (64)</li>
                                    <li class="hide"><a href="/business/Companies/Transurban">Transurban</a> (62)</li>
                                    <li class="hide"><a href="/business/Companies/AGL">AGL</a> (61)</li>
                                    <li class="hide"><a href="/business/Companies/Macquarie-Bank">Macquarie Bank</a> (61)</li>
                                    <li class="hide"><a href="/business/Companies/Asciano">Asciano</a> (58)</li>
                                    <li class="hide"><a href="/business/Companies/Foster-s">Foster's</a> (56)</li>
                                    <li class="hide"><a href="/business/Companies/Lihir-Gold">Lihir Gold</a> (55)</li>
                                    <li class="hide"><a href="/business/Companies/Newcrest-Mining">Newcrest Mining</a> (55)</li>
                                    <li class="hide"><a href="/business/Companies/Great-Southern">Great Southern</a> (54)</li>
                                    <li class="hide"><a href="/business/Companies/Mirvac">Mirvac</a> (52)</li>
                                    <li class="hide"><a href="/business/Companies/Consolidated-Media-Holdings">Consolidated Media Holdings</a> (51)</li>
                                    <li class="hide"><a href="/business/Companies/Goodman">Goodman</a> (50)</li>
                                    <li class="hide"><a href="/business/Companies/Suncorp-Metway">Suncorp-Metway</a> (50)</li>
                                    <li class="hide"><a href="/business/Companies/Tabcorp">Tabcorp</a> (49)</li>
                                    <li class="hide"><a href="/business/Companies/ABC-Learning">ABC Learning</a> (49)</li>
                                    <li class="hide"><a href="/business/Companies/Timbercorp">Timbercorp</a> (48)</li>
                                    <li class="hide"><a href="/business/Companies/BankWest">BankWest</a> (48)</li>
                                    <li class="hide"><a href="/business/Companies/CSR">CSR</a> (47)</li>
                                    <li class="hide"><a href="/business/Companies/Ten-Network">Ten Network</a> (46)</li>
                                    <li class="hide"><a href="/business/Companies/Gunns">Gunns</a> (45)</li>
                                    <li class="hide"><a href="/business/Companies/JB-Hi-Fi">JB Hi-Fi</a> (44)</li>
                                    <li class="hide"><a href="/business/Companies/Elders">Elders</a> (44)</li>
                                    <li class="hide"><a href="/business/Companies/Stockland">Stockland</a> (44)</li>
                                    <li class="hide"><a href="/business/Companies/James-Hardie">James Hardie</a> (43)</li>
                                    <li class="hide"><a href="/business/Companies/GPT">GPT</a> (42)</li>
                                    <li class="hide"><a href="/business/Companies/Amcor">Amcor</a> (41)</li>
                                    <li class="hide"><a href="/business/Companies/Nufarm">Nufarm</a> (41)</li>
                                    <li class="hide"><a href="/business/Companies/Felix-Resources">Felix Resources</a> (40)</li>
                                    <li class="hide"><a href="/business/Companies/Lion-Nathan">Lion Nathan</a> (39)</li>
                                    <li class="hide"><a href="/business/Companies/Metcash">Metcash</a> (39)</li>
                                    <li class="hide"><a href="/business/Companies/Toll-Holdings">Toll Holdings</a> (37)</li>
                                    <li class="hide"><a href="/business/Companies/Caltex">Caltex</a> (36)</li>
                                    <li class="hide"><a href="/business/Companies/Brambles">Brambles</a> (35)</li>
                            <li class="more"><a href="#">More Companies</a></li>
                    </ul>
                </div>
                            <div class="cN-groupNavigator close">
                    <h4><a href="#">People<span>Expand</span> (2513)</a></h4>
                    <ul>
                                <li class="all"><a href="/business/People">All People</a> (2513)</li>
                                <li><a href="/business/People/James-Packer">James Packer</a> (107)</li>
                                <li><a href="/business/People/Rupert-Murdoch">Rupert Murdoch</a> (86)</li>
                                <li><a href="/business/People/Mike-Smith">Mike Smith</a> (73)</li>
                                <li><a href="/business/People/Ralph-Norris">Ralph Norris</a> (69)</li>
                                <li><a href="/business/People/Cameron-Clyne">Cameron Clyne</a> (68)</li>
                                    <li class="hide"><a href="/business/People/Gail-Kelly">Gail Kelly</a> (64)</li>
                                    <li class="hide"><a href="/business/People/Kerry-Stokes">Kerry Stokes</a> (61)</li>
                                    <li class="hide"><a href="/business/People/David-Thodey">David Thodey</a> (58)</li>
                                    <li class="hide"><a href="/business/People/Marius-Kloppers">Marius Kloppers</a> (47)</li>
                                    <li class="hide"><a href="/business/People/Sol-Trujillo">Sol Trujillo</a> (46)</li>
                                    <li class="hide"><a href="/business/People/Lachlan-Murdoch">Lachlan Murdoch</a> (46)</li>
                                    <li class="hide"><a href="/business/People/Andrew-Forrest">Andrew Forrest</a> (41)</li>
                                    <li class="hide"><a href="/business/People/Alan-Joyce">Alan Joyce</a> (38)</li>
                                    <li class="hide"><a href="/business/People/Craig-Dunn">Craig Dunn</a> (38)</li>
                                    <li class="hide"><a href="/business/People/Don-Argus">Don Argus</a> (35)</li>
                            <li class="more"><a href="#">More People</a></li>
                    </ul>
                </div>
                            <div class="cN-groupNavigator close">
                    <h4><a href="#">Topics<span>Expand</span> (1784)</a></h4>
                    <ul>
                                <li class="all"><a href="/business/Topics">All Topics</a> (1784)</li>
                                <li><a href="/business/Topics/Interest-Rates">Interest Rates</a> (827)</li>
                                <li><a href="/business/Topics/Global-Financial-Crisis">Global Financial Crisis</a> (739)</li>
                                <li><a href="/business/Topics/Superannuation">Superannuation</a> (221)</li>
                                <li><a href="/business/Topics/Pension">Pension</a> (152)</li>
                                <li><a href="/business/Topics/Stimulus-Package">Stimulus Package</a> (124)</li>
                                    <li class="hide"><a href="/business/Topics/Budget-Deficit">Budget Deficit</a> (55)</li>
                            <li class="more"><a href="#">More Topics</a></li>
                    </ul>
                </div>
                            <div class="cN-groupNavigator close">
                    <h4><a href="#">Organisations<span>Expand</span> (1611)</a></h4>
                    <ul>
                                <li class="all"><a href="/business/Organisations">All Organisations</a> (1611)</li>
                                <li><a href="/business/Organisations/Reserve-Bank">Reserve Bank</a> (846)</li>
                                <li><a href="/business/Organisations/ASX">ASX</a> (365)</li>
                                <li><a href="/business/Organisations/ASIC">ASIC</a> (266)</li>
                                <li><a href="/business/Organisations/ACCC">ACCC</a> (122)</li>
                                <li><a href="/business/Organisations/APRA">APRA</a> (52)</li>
                    </ul>
                </div>
</div>






<!-- cT-storyTools -->
<div class="cT-storyTools cfix">
	<!-- cT-strapHeading -->
	<h3 class="cT-strapHeading">Story Tools</h3>

    <ul class="accessibleStoryTools">
        <li class="facebook"><a href="http://www.facebook.com/share.php?u=http://www.smh.com.au/business/the-boards-next-fear-the-female-quota-********-lteq.html" onclick="javascript:u='http://www.smh.com.au/business/the-boards-next-fear-the-female-quota-********-lteq.html';t='The board’s next fear: the female quota';window.open('http://www.facebook.com/sharer.php?u='+encodeURIComponent(u)+'&t='+encodeURIComponent(t),'sharer','toolbar=0,status=0,width=626,height=436');return false;" rel="nofollow">Share on Facebook</a></li>
		<li class="email"><a href="/action/emailToFriend?id=1017890" onclick="var popup =window.open('/action/emailToFriend?id=1017890','EmailArticle','toolbar=no,menubar=no,width=760,height=680,resizable=yes,menubar=no,status=no,scrollbars=no');popup.focus();return false" title="Email to a friend" rel="nofollow">Email this story</a></li>
		<li class="print"><a href="/action/printArticle?id=1017890" onclick="var popup =window.open('/action/printArticle?id=1017890','PrintArticle','toolbar=no,menubar=no,width=1024,height=620,resizable=yes,menubar=no,status=no,scrollbars=yes');popup.focus();return false" title="Print this story" rel="nofollow">Print this story</a></li>
	</ul>

    <div id="adspot-180x44" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-180x44",
            iframeId: "adspot-180x44-iframe",
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "180x44"
        }),getAdParams("180x44"))

            ,height: 75
            ,width: 180
            })
        );
    }
);

</script>
</div>
<!-- cT-storyTools -->

            <div class="cT-storyTools cfix">
    <h3 class="cT-strapHeading">SMH Jobs</h3>
    <iframe width="180" height="400" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" src="http://commercial.adperfect.com/adserving/dynamicwebads/adframe.php?AdTagID=C0A801C31d59123256lhqjCAAA14&ap_params=&r=444525"></iframe>
</div>

        </div><!-- left sidebar -->


        <div class="span-16 last">









        </div>


    </div><!-- content -->

    <!-- Aside (class according to new HTML5 element 'aside'; was 'sidebar') -->
    <div class="aside span-8 last">

        <div id="adspot-300x250-pos-1" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-300x250-pos-1",
            iframeId: "adspot-300x250-pos-1-iframe",
            params: $merge($merge(FD.baseAd.params, {
                    pos: 1,
                    adtype: 'doubleisland',
                aamsz : "300x250"
        }),getAdParams("300x250"))

            ,addSmall: true
            })
        );
    }
);

</script>

        <div class="sstrap executivestyle cfix cS-sectionPromo">

                <h2 class="cfix"><a href="http://smh.com.au/executive-style/">Executive Style</a><span class="inlineRight"><div id="adSpot-lastop"></div></span></h2>
			<ul class="inlineRight">
				<li><a href="http://smh.com.au/executive-style/business-travel/">Travel</a></li>
				<li><a href="http://smh.com.au/executive-style/motors/">Motors</a></li>
				<li><a href="http://smh.com.au/executive-style/culture/">Culture</a></li>
				<li><a href="http://smh.com.au/executive-style/gadgets/">Gadgets</a></li>
				<li><a href="http://smh.com.au/executive-style/management">Management</a></li>
				<li class="last"><a href="http://smh.com.au/executive-style/style">Style</a></li>
            </ul>
        <!--@List: Executive Style Puff -->
            <!--@Asset: 1015488 -->
                <div class="puff">
                    <a href="http://www.smh.com.au/executive-style/fitness/adventurer-aims-for-the-north-pole-********-lrk0.html">
                            <img src="http://images.smh.com.au/2010/01/05/1015720/rhs300-smitheringale-300x0.jpg" alt="Tom Smitheringale."/>
                    </a>
                    <div class="caption">
                        <h3><a href="http://www.smh.com.au/executive-style/fitness/adventurer-aims-for-the-north-pole-********-lrk0.html" title="Adventurer aims for the North Pole">Adventurer aims for the North Pole</a></h3>
                        <p>The risk of a frozen death does not phase Tom Smitheringale. He will trek to the North Pole, alone.</p>
                    </div>
                </div>
            <!--@RelateImage-->
            <!--/@Asset-->
            <!--@Asset: 1014788 -->
                <div class="wof">
                    <h3><a href="http://www.smh.com.au/executive-style/fitness/sleep-loss-may-affect-health-by-curbing-exercise-********-lr0k.html"><strong>Physical activity:</strong> </a> <a href="http://www.smh.com.au/executive-style/fitness/sleep-loss-may-affect-health-by-curbing-exercise-********-lr0k.html" title="Impact of sleep loss on health">Impact of sleep loss on health</a></h3>
                    <p>
                    <a href="http://www.smh.com.au/executive-style/fitness/sleep-loss-may-affect-health-by-curbing-exercise-********-lr0k.html">
                        <img src="http://images.smh.com.au/2009/09/08/719064/thumb140_gym-90x60.jpg" alt="A gym." />
                    </a>
                    Over time, a lack of sleep could affect a person's weight and general health because of the impact is has on physical activity, study shows.
                    </p>
                </div>
            <!--@RelateImage-->
            <!--/@Asset-->
            <!--@Asset: 1010931 -->
                <div class="wof">
                    <h3><a href="http://www.smh.com.au/executive-style/culture/when-pinball-was-king-20100104-lo1f.html"><strong>Arcade action:</strong> </a> <a href="http://www.smh.com.au/executive-style/culture/when-pinball-was-king-20100104-lo1f.html" title="When pinball was king">When pinball was king</a></h3>
                    <p>
                    <a href="http://www.smh.com.au/executive-style/culture/when-pinball-was-king-20100104-lo1f.html">
                        <img src="http://images.smh.com.au/2010/01/04/1013000/thumb140_pinball-90x60.jpg" alt="One of Tony Mather's pinball  machines." />
                    </a>
                    It was outlawed in the '40s and killed off by Space Invaders in the '80s. But for a few glorious decades pinball ruled.
                    </p>
                </div>
            <!--@RelateImage-->
            <!--/@Asset-->
            <!--@Asset: 731361 -->
                <div class="wof">
                    <h3><a href="http://blogs.theage.com.au/executive-style/allmenareliars/"><strong>All Men are Liars:</strong> </a> <a href="http://blogs.theage.com.au/executive-style/allmenareliars/" title="Pardon the self promotion">Pardon the self promotion</a></h3>
                    <p>
                    <a href="http://blogs.theage.com.au/executive-style/allmenareliars/">
                        <img src="http://images.smh.com.au/2009/11/11/851134/thumb140_samdebrito_headshot-index-90x60.jpg" alt="Sam De Brito, All Men are Liars blog." />
                    </a>
                    Nominate All Men Are Liars for the 2010 Weblog Awards.
                    </p>
                </div>
            <!--@RelateImage-->
            <!--/@Asset-->
            <!--@Asset: 1005318 -->
                <div class="wof">
                    <h3><a href="http://www.smh.com.au/photogallery/executive-style/luxury/worlds-top-ski-destinations/20091230-ljpi.html"><strong>Snow blind:</strong> </a> <a href="http://www.smh.com.au/photogallery/executive-style/luxury/worlds-top-ski-destinations/20091230-ljpi.html" title="World's top ski destinations">World's top ski destinations</a></h3>
                    <p>
                    <a href="http://www.smh.com.au/photogallery/executive-style/luxury/worlds-top-ski-destinations/20091230-ljpi.html">
                        <img src="http://images.smh.com.au/2009/12/31/1006160/th_skiwhistler3-90x60.jpg" alt="skiing" />
                    </a>
                    For die hard powder-hounds and devoted apres-skiers alike, these resorts are the best in snow business.
                    </p>
                </div>
            <!--@RelateImage-->
            <!--/@Asset-->
        <!--/@List-->
</div>


        <div id="adspot-149x170" class="ad adSpot-twin"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-149x170",
            iframeId: "adspot-149x170-iframe",
                src:[
                        FD.baseAd.src + 'POS=1/',
                        FD.baseAd.src + 'POS=2/'
                ],
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "149x170"
        }),getAdParams("149x170"))

            ,addSmall: "top"
            ,smallText: "Featured advertisers"
            })
        );
    }
);

</script>

        <?xml version="1.0" encoding="UTF-8"?><h3 class="cN-headerRich bankRates">Today's bank rates<span class="ad"><a href="http://mozo.com.au/">Powered by Mozo</a></span></h3><ul class="cN-tabBox cS-bankRates accessibleTab" id="cN-tabBox-mozo"><li class="tab1 selected"><h4><a rel="nofollow" href="http://mozo.com.au/activity/record_widget_link/3/savings%20accounts?tabName=Savings Accounts#" class="cN-externalTarget">Savings Accounts</a></h4><div><p><span>Percentages denote the </span>interest rate</p><dl><dt><strong>UBank</strong>USaver</dt><dd><em>5.51%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=127&amp;product_type=SavingsAccount&amp;provider_id=172&amp;path=/feed/fairfax_widget_03&amp;position=1&amp;the_url=adsfac.net/link.asp%3fcc=UBA006.100897.0%26clk=1%26creativeID=147817%26ord=[timestamp]&amp;partial=fairfax_widget_03&amp;tab_name=savings accounts&amp;alternatives=[104, 38, 27]&amp;tabName=Savings Accounts" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>ANZ</strong>Online Saver</dt><dd><em>5.25%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=104&amp;product_type=SavingsAccount&amp;provider_id=7&amp;path=/feed/fairfax_widget_03&amp;position=2&amp;the_url=ad.au.doubleclick.net/clk;*********;********;z&amp;partial=fairfax_widget_03&amp;tab_name=savings accounts&amp;alternatives=[127, 38, 27]&amp;tabName=Savings Accounts" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>ING DIRECT</strong>Savings Maximiser</dt><dd><em>4.25%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=38&amp;product_type=SavingsAccount&amp;provider_id=65&amp;path=/feed/fairfax_widget_03&amp;position=3&amp;the_url=clk.atdmt.com/MOS/go/*********/direct/01/&amp;partial=fairfax_widget_03&amp;tab_name=savings accounts&amp;alternatives=[127, 104, 27]&amp;tabName=Savings Accounts" target="_blank" rel="nofollow">Get info</a></dd></dl><dl class="last"><dt><strong>Suncorp</strong>eOptions</dt><dd><em>4.00%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=27&amp;product_type=SavingsAccount&amp;provider_id=115&amp;path=/feed/fairfax_widget_03&amp;position=4&amp;the_url=www.s2d6.com/x/%3fx=c%26z=s%26v=1276499%26k=[NETWORKID]  &amp;partial=fairfax_widget_03&amp;tab_name=savings accounts&amp;alternatives=[127, 104, 38]&amp;tabName=Savings Accounts" target="_blank" rel="nofollow">Get info</a></dd></dl><a class="more" target="_blank" href="/savings-accounts">Compare all savings accounts</a></div></li><li class="tab2"><h4><a rel="nofollow" href="http://mozo.com.au/activity/record_widget_link/3/term%20deposits?tabName=Term Deposits#" class="cN-externalTarget">Term Deposits</a></h4><div><p><span>Percentages denote the </span>interest rate</p><dl><dt><strong>ING DIRECT</strong> 1 year annual</dt><dd><em>6.10%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=148&amp;product_type=TermDeposit&amp;provider_id=65&amp;path=/feed/fairfax_widget_03&amp;position=1&amp;the_url=clk.atdmt.com/MOS/go/*********/direct/01/&amp;partial=fairfax_widget_03&amp;tab_name=term deposits&amp;alternatives=[336, 311, 243]&amp;tabName=Term Deposits" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>Citibank</strong> 1 year annual</dt><dd><em>6.00%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=336&amp;product_type=TermDeposit&amp;provider_id=29&amp;path=/feed/fairfax_widget_03&amp;position=2&amp;the_url=www.citibank.com.au/td&amp;partial=fairfax_widget_03&amp;tab_name=term deposits&amp;alternatives=[148, 311, 243]&amp;tabName=Term Deposits" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>Laiki Bank</strong> 180 days</dt><dd><em>5.70%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=311&amp;product_type=TermDeposit&amp;provider_id=167&amp;path=/feed/fairfax_widget_03&amp;position=3&amp;the_url=www.laiki.com/web/w3au.nsf/WebPromotionDocsByID/ID-7564BA120DAB256BCA2574FA0019BDFC&amp;partial=fairfax_widget_03&amp;tab_name=term deposits&amp;alternatives=[148, 336, 243]&amp;tabName=Term Deposits" target="_blank" rel="nofollow">Get info</a></dd></dl><dl class="last"><dt><strong>Macquarie</strong> 90 days</dt><dd><em>5.50%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=243&amp;product_type=TermDeposit&amp;provider_id=71&amp;path=/feed/fairfax_widget_03&amp;position=4&amp;the_url=members.commissionmonster.com/z/87402/11571/&amp;partial=fairfax_widget_03&amp;tab_name=term deposits&amp;alternatives=[148, 336, 311]&amp;tabName=Term Deposits" target="_blank" rel="nofollow">Get info</a></dd></dl><a class="more" target="_blank" href="/term-deposits">Compare all term deposits</a></div></li><li class="tab3"><h4><a rel="nofollow" href="http://mozo.com.au/activity/record_widget_link/3/credit%20cards?tabName=Credit Cards#" class="cN-externalTarget">Credit Cards</a></h4><div><p><span>Percentages denote the </span>interest rate</p><dl><dt><strong>BankWest</strong>Lite</dt><dd><em>9.99%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=66&amp;product_type=CreditCard&amp;provider_id=19&amp;path=/feed/fairfax_widget_03&amp;position=1&amp;the_url=clk.atdmt.com/IKS/go/*********/direct/01/&amp;partial=fairfax_widget_03&amp;tab_name=credit cards&amp;alternatives=[22, 67, 89]&amp;tabName=Credit Cards" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>NAB</strong>Low Rate</dt><dd><em>11.74%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=22&amp;product_type=CreditCard&amp;provider_id=85&amp;path=/feed/fairfax_widget_03&amp;position=2&amp;the_url=clk.atdmt.com/NOZ/go/*********/direct/01/&amp;partial=fairfax_widget_03&amp;tab_name=credit cards&amp;alternatives=[66, 67, 89]&amp;tabName=Credit Cards" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>ANZ</strong>Low Rate</dt><dd><em>12.49%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=67&amp;product_type=CreditCard&amp;provider_id=7&amp;path=/feed/fairfax_widget_03&amp;position=3&amp;the_url=ad.au.doubleclick.net/clk;*********;********;d&amp;partial=fairfax_widget_03&amp;tab_name=credit cards&amp;alternatives=[66, 22, 89]&amp;tabName=Credit Cards" target="_blank" rel="nofollow">Get info</a></dd></dl><dl class="last"><dt><strong>Citibank</strong>Platinum Card</dt><dd><em>20.49%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=89&amp;product_type=CreditCard&amp;provider_id=29&amp;path=/feed/fairfax_widget_03&amp;position=4&amp;the_url=www.citibank.com.au/cardsoffer/0909PlatFlyFree.htm%3fCode=P1C79HYQ&amp;partial=fairfax_widget_03&amp;tab_name=credit cards&amp;alternatives=[66, 22, 67]&amp;tabName=Credit Cards" target="_blank" rel="nofollow">Get info</a></dd></dl><a class="more" target="_blank" href="/credit-cards">Compare all credit cards</a></div></li><li class="tab4"><h4><a rel="nofollow" href="http://mozo.com.au/activity/record_widget_link/3/home%20loans?tabName=Home Loans#" class="cN-externalTarget">Home Loans</a></h4><div><p><span>Percentages denote the </span>comparison rate</p><dl><dt><strong>MyRate.com.au</strong>Advantage Loan </dt><dd><em>5.84%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=289&amp;product_type=HomeLoan&amp;provider_id=83&amp;path=/feed/fairfax_widget_03&amp;position=1&amp;the_url=www.myrate.com.au/save%3fa_id=mozo_listing&amp;partial=fairfax_widget_03&amp;tab_name=home loans&amp;alternatives=[372, 379, 170]&amp;tabName=Home Loans" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>Laiki Bank</strong>Options Plus</dt><dd><em>6.00%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=372&amp;product_type=HomeLoan&amp;provider_id=167&amp;path=/feed/fairfax_widget_03&amp;position=2&amp;the_url=www.laiki.com/web/w3au.nsf/WebPromotionDocsByID/ID-F3FBE78446C1B0BFCA2573330023C634&amp;partial=fairfax_widget_03&amp;tab_name=home loans&amp;alternatives=[289, 379, 170]&amp;tabName=Home Loans" target="_blank" rel="nofollow">Get info</a></dd></dl><dl><dt><strong>Aussie</strong>Classic Line of Credit</dt><dd><em>6.52%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=379&amp;product_type=HomeLoan&amp;provider_id=10&amp;path=/feed/fairfax_widget_03&amp;position=3&amp;the_url=clk.atdmt.com/AUS/go/*********/direct/01/ &amp;partial=fairfax_widget_03&amp;tab_name=home loans&amp;alternatives=[289, 372, 170]&amp;tabName=Home Loans" target="_blank" rel="nofollow">Get info</a></dd></dl><dl class="last"><dt><strong>Aussie</strong>Standard Fixed</dt><dd><em>6.77%</em><span/></dd><dd class="last"><a class="applyParamsForExternal" href="#http://mozo.com.au/activity/record_gts2?product_id=170&amp;product_type=HomeLoan&amp;provider_id=10&amp;path=/feed/fairfax_widget_03&amp;position=4&amp;the_url=clk.atdmt.com/AUS/go/*********/direct/01/ &amp;partial=fairfax_widget_03&amp;tab_name=home loans&amp;alternatives=[289, 372, 379]&amp;tabName=Home Loans" target="_blank" rel="nofollow">Get info</a></dd></dl><a class="more" target="_blank" href="/home-loans">Compare all home loans</a></div></li></ul>

        <div id="adspot-295x60" class="ad "></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-295x60",
            iframeId: "adspot-295x60-iframe",
                src:[
                        FD.baseAd.src + 'POS=1/',
                        FD.baseAd.src + 'POS=2/',
                        FD.baseAd.src + 'POS=3/'
                ],
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "295x60"
        }),getAdParams("295x60"))

            ,addSmall: "top"
            ,smallText: "Sponsored links"
            })
        );
    }
);

</script>

        <div id="adspot-1x4" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-1x4",
            iframeId: "adspot-1x4-iframe",
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "1x4"
        }),getAdParams("1x4"))

            ,height: 150
            ,width: 300
            ,addSmall: true
            })
        );
    }
);

</script>

        <div id="adspot-300x250-pos-2" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-300x250-pos-2",
            iframeId: "adspot-300x250-pos-2-iframe",
            params: $merge($merge(FD.baseAd.params, {
                    pos: 2,
                aamsz : "300x250"
        }),getAdParams("300x250"))

            ,addSmall: true
            })
        );
    }
);

</script>




    </div>

    <!-- Footer fN-footerNetwork (class according to new HTML5 element 'footer'; scope: network-wide) -->
<div class="footer span-24">
    <script type="text/javascript">
function trackOmnitureClick(anchor,identifier) {
  var hash = "";
  var hashIndex = anchor.href.indexOf("#");
  if (hashIndex > -1) {
      hash = anchor.href.substring(hashIndex);
      anchor.href = anchor.href.substring(0, hashIndex);
  }
  if(anchor.href.indexOf("s_rid") > -1) {
      // Remove any s_rid's
      anchor.href = anchor.href.replace(/[\??|&?]s_rid=(.*)/i, "");
  }
  if(anchor.href.indexOf("s_cid") > -1) {
      // Strip any s_cid's
      anchor.href = anchor.href.replace(/[\??|&?]s_cid=(.*)/i, "");
  }
  var query = (anchor.href.indexOf("?") > -1) ? "&" : "?";
  anchor.href += query + "s_rid="+identifier;
  if (hashIndex > -1) {
      anchor.href += hash;
  }
  return true;
}
</script>


<div class="c5 classifieds cfix">

                  <div class="s1 cBusinessDay">
                      <h2><a title="Business" onclick="trackOmnitureClick(this,'smh:rainbowstrip:label:fatcats_172x115');" href="http://www.businessday.com.au">Business</a></h2>
                            <div class="puff">
                                    <a href="http://www.smh.com.au/business/executive-pay-given-a-reprieve-20100103-lnas.html" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content1:04-01:fatcats_172x115');" ><img src="http://images.smh.com.au/2010/01/04/1010993/fatcats_172x115-172x115.jpg" width="172" alt="Fat cats"/></a>

                                <h5><a href="http://www.smh.com.au/business/executive-pay-given-a-reprieve-20100103-lnas.html" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content2:04-01:fatcats_172x115:executivepaygivenareprieve');" title="Executive pay given a reprieve">Executive pay given a reprieve</a></h5>
                            </div>
                            <ul>
                                <li class="lBusinessDay"><a href="http://www.businessday.com.au" onclick="trackOmnitureClick(this,'smh:rainbowstrip:logo:04-01:fatcats_172x115');">Businessday.com.au</a></li>
                           <li><a href="http://www.smh.com.au/business/" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet1:04-01:fatcats_172x115:latestbusinessnews');">Latest Business news</a></li>
                           <li><a href="http://www.smh.com.au/executive-style/" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet2:04-01:fatcats_172x115:executivestyle');">Executive Style</a></li>
                           <li><a href="http://www.smh.com.au/small-business" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet3:04-01:fatcats_172x115:mysmallbusiness');">MySmallBusiness</a></li>
                      </ul>
                  </div>
                  <div class="s2 cBusinessDay">
                      <h2><a title="Business" onclick="trackOmnitureClick(this,'smh:rainbowstrip:label:nab_172x115');" href="http://www.businessday.com.au">Business</a></h2>
                            <div class="puff">
                                    <a href="http://www.smh.com.au/business/nab-set-to-pounce-on-northern-rock-20100104-lnxs.html" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content1:04-01:nab_172x115');" ><img src="http://images.smh.com.au/2010/01/04/1012429/nab_172x115-172x115.jpg" width="172" alt="NAB"/></a>

                                <h5><a href="http://www.smh.com.au/business/nab-set-to-pounce-on-northern-rock-20100104-lnxs.html" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content2:04-01:nab_172x115:nabsettopounceonnorthernrock');" title="NAB set to pounce on Northern Rock">NAB set to pounce on Northern Rock</a></h5>
                            </div>
                            <ul>
                                <li class="lBusinessDay"><a href="http://www.businessday.com.au" onclick="trackOmnitureClick(this,'smh:rainbowstrip:logo:04-01:nab_172x115');">Businessday.com.au</a></li>
                           <li><a href="http://www.smh.com.au/business/markets" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet1:04-01:nab_172x115:marketreports');">Market Reports</a></li>
                           <li><a href="http://www.smh.com.au/business/money/" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet2:04-01:nab_172x115:moneynews');">Money news</a></li>
                           <li><a href="http://www.smh.com.au/business/property" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet3:04-01:nab_172x115:propertyfocus');">Property Focus</a></li>
                      </ul>
                  </div>
                  <div class="s3 cSmh">
                      <h2><a title="Video" onclick="trackOmnitureClick(this,'smh:rainbowstrip:label:dustin');" href="http://www.smh.com.au">Video</a></h2>
                            <div class="puff">
                                    <a href="http://media.fairfax.com.au/?rid=56862" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content1:05-01:dustin');" ><img src="http://images.smh.com.au/2010/01/05/1015399/dustin-172x115.jpg" width="172" alt="Dustin Hoffman"/></a>

                                <h5><a href="http://media.fairfax.com.au/?rid=56862" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content2:05-01:dustin:dustinhoffmansitaliantourismad');" title="Dustin Hoffman's Italian tourism ad">Dustin Hoffman's Italian tourism ad</a></h5>
                            </div>
                            <ul>
                                <li class="lSmh"><a href="http://www.smh.com.au" onclick="trackOmnitureClick(this,'smh:rainbowstrip:logo:05-01:dustin');">Smh.com.au</a></li>
                           <li><a href="http://media.smh.com.au/lifestyle/essentials" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet1:07-12:dustin:life&stylevideos');">Life & Style videos</a></li>
                           <li><a href="http://media.smh.com.au/entertainment/red-carpet" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet2:07-12:dustin:entertainmentvideos');">Entertainment videos</a></li>
                           <li><a href="http://media.smh.com.au/business/businessday" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet3:07-12:dustin:businessvideos');">Business videos</a></li>
                      </ul>
                  </div>
                  <div class="s4 cInvestSmart">
                      <h2><a title="InvestSMART" onclick="trackOmnitureClick(this,'smh:rainbowstrip:label:2009lights');" href="http://www.investsmart.com.au">InvestSMART</a></h2>
                            <div class="puff">
                                    <a href="http://www.investsmart.com.au/managed-funds/top-managed-funds.asp?s_cid=promostrip:top2009" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content1:05-01:2009lights');" ><img src="http://images.smh.com.au/2010/01/05/1015432/2009lights-172x115.gif" width="172" alt="2009"/></a>

                                <h5><a href="http://www.investsmart.com.au/managed-funds/top-managed-funds.asp?s_cid=promostrip:top2009" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content2:05-01:2009lights:topperforminginvestmentfundsattheendof2009');" title="Top Performing Investment Funds at the end of 2009">Top Performing Investment Funds at the end of 2009</a></h5>
                            </div>
                            <ul>
                                <li class="lInvestSmart"><a href="http://www.investsmart.com.au" onclick="trackOmnitureClick(this,'smh:rainbowstrip:logo:05-01:2009lights');">Investsmart.com.au</a></li>
                           <li><a href="http://www.investsmart.com.au/share_trading/open_account.asp" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet1:23-10:2009lights:openanaccount');">Open an account</a></li>
                           <li><a href="http://www.investsmart.com.au/managed-funds/top-managed-funds.asp" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet2:23-10:2009lights:topperformingmanagedfunds');">Top Performing Managed Funds</a></li>
                           <li><a href="http://www.investsmart.com.au/share_trading/open_account.asp" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet3:23-10:2009lights:sharetrading');">Share Trading</a></li>
                      </ul>
                  </div>
                  <div class="s5 cSmh">
                      <h2><a title="Sport" onclick="trackOmnitureClick(this,'smh:rainbowstrip:label:jwrainbow3');" href="http://www.smh.com.au">Sport</a></h2>
                            <div class="puff">
                                    <a href="http://pages.email.fairfax.com.au/smh/jwsummerofcricket?s_cid=rainbow:smh:johnniewalkercricket:dec09:feb10" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content1:04-01:jwrainbow3');" ><img src="http://images.smh.com.au/2010/01/04/1012523/JWRainbow3-172x115.jpg" width="172" alt="WIN"/></a>

                                <h5><a href="http://pages.email.fairfax.com.au/smh/jwsummerofcricket?s_cid=rainbow:smh:johnniewalkercricket:dec09:feb10" rel="nofollow" onclick="trackOmnitureClick(this,'smh:rainbowstrip:content2:04-01:jwrainbow3:winweeklyjohnniewalkerprizes');" title="Win weekly Johnnie Walker prizes">Win weekly Johnnie Walker prizes</a></h5>
                            </div>
                            <ul>
                                <li class="lSmh"><a href="http://www.smh.com.au" onclick="trackOmnitureClick(this,'smh:rainbowstrip:logo:04-01:jwrainbow3');">Smh.com.au</a></li>
                           <li><a href="http://www.smh.com.au/sport/cricket" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet1:21-12:jwrainbow3:cricketnews');">Cricket news</a></li>
                           <li><a href="http://www.smh.com.au/sport/cricket" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet2:21-12:jwrainbow3:livecricketscores');">Live cricket scores</a></li>
                           <li><a href="http://www.smh.com.au/sport/" onclick="trackOmnitureClick(this,'smh:rainbowstrip:bullet3:21-12:jwrainbow3:latestinsport');">Latest in Sport</a></li>
                      </ul>
                  </div>

</div>



    <div class="c5 affStrip">
    <h2>Compare and Save</h2>
    <ul>
        <li ><a href="http://broadband.smh.com.au/Broadband/" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:link')">Broadband</a></li>
        <li ><a href="http://smh.goswitch.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:link')">Energy</a></li>
        <li ><a href="http://mobile-phones.smh.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:link')">Mobile</a></li>
        <li ><a href="http://compare.smh.com.au/home-loans" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:link')">Home Loans</a></li>
        <li class="last"><a href="http://compare.smh.com.au/savings-accounts" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:link')">Savings Accounts</a></li>
</ul>

    <span></span>
        <div>
            <h3><a href="http://compare.smh.com.au/term-deposits" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content0')">Grab what you can!</a></h3>
                <a href="http://compare.smh.com.au/term-deposits" rel="nofollow" onclick="trackOmnitureClick(this,'smh:affiliatestrip:content0:td---grab 5 jan');" ><img src="http://images.smh.com.au/2010/01/05/1014762/TD---Grab 5 Jan-60x90.jpg" width="60" alt="TD---Grab 5 Jan"/></a>

            <p><a href="http://compare.smh.com.au/term-deposits" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content0')">A show of hands for the best term deposit.</a></p>
            <p class="links"><a href="http://compare.smh.com.au/term-deposits" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content0')">Compare all Term Deposits</a></p>
    </div>
    <div>
            <h3><a href="http://compare.smh.com.au/savings-accounts" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content1')">Gimme gimme gimme!</a></h3>
                <a href="http://compare.smh.com.au/savings-accounts" rel="nofollow" onclick="trackOmnitureClick(this,'smh:affiliatestrip:content1:cc---gold-diggers 21 dec');" ><img src="http://images.smh.com.au/2009/12/21/991026/CC---Gold-Diggers 21 Dec-60x90.jpg" width="60" alt="CC---Gold-Diggers 21 Dec"/></a>

            <p><a href="http://compare.smh.com.au/savings-accounts" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content1')">Earn 5.51% on your savings</a></p>
            <p class="links"><a href="http://compare.smh.com.au/savings-accounts" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content1')">Compare all Savings Accounts</a></p>
    </div>
    <div>
            <h3><a href="http://thecellar.thecorridor.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content2')">Discounted Wine @ The Corridor</a></h3>
                <a href="http://thecellar.thecorridor.com.au/" rel="nofollow" onclick="trackOmnitureClick(this,'smh:affiliatestrip:content2:ishop 14 dec');" ><img src="http://images.smh.com.au/2009/12/14/968217/iShop 14 Dec-60x90.jpg" width="60" alt="iShop 14 Dec"/></a>

            <p><a href="http://thecellar.thecorridor.com.au/" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content2')">Up to 25% off all off our wine @ The Corridor. Over 35 well known brands on sale</a></p>
            <p class="links"><a href="http://thecellar.thecorridor.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content2')">Compare our prices today</a></p>
    </div>
    <div>
            <h3><a href="http://mobile-phones.smh.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content3')">Mobile Phone Plans</a></h3>
                <a href="http://mobile-phones.smh.com.au" rel="nofollow" onclick="trackOmnitureClick(this,'smh:affiliatestrip:content3:mobile_60x90_phone 18 dec');" ><img src="http://images.smh.com.au/2009/12/18/984193/mobile_60x90_phone 18 Dec-60x90.gif" width="60" alt="mobile_60x90_phone 18 Dec"/></a>

            <p><a href="http://mobile-phones.smh.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content3')">Compare iPhones and Mobile Plans for Christmas.</a></p>
            <p class="links"><a href="http://mobile-phones.smh.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content3')">Compare Mobile Phones & Plans</a></p>
    </div>
    <div class="last">
            <h3><a href="http://broadband.smh.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content4')">Broadband Plans</a></h3>
                <a href="http://broadband.smh.com.au/Broadband/" rel="nofollow" onclick="trackOmnitureClick(this,'smh:affiliatestrip:content4:bband_60x90_laptop 18 dec');" ><img src="http://images.smh.com.au/2009/12/18/984311/bband_60x90_laptop 18 Dec-60x90.gif" width="60" alt="bband_60x90_laptop 18 Dec"/></a>

            <p><a href="http://broadband.smh.com.au/Broadband/" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content4')">Broadband Plans to cope with your Christmas downloads</a></p>
            <p class="links"><a href="http://broadband.smh.com.au" onclick="trackOmnitureClick(this, 'smh:affiliatestrip:content4')">Compare all Broadband Plans</a></p>
    </div>

</div>


<!-- Reader's most viewed -->
<div class="c5 top5 cfix">
    <h2 class="cfix">Readers' most viewed</h2>

            <div class="s1 lBT">
                <a href="http://www.brisbanetimes.com.au">Most viewed articles on Brisbane Times</a>
                <h5>Top 5 <a href="http://www.brisbanetimes.com.au/business">Business</a> articles</h5>
    <ol>
            <li><a href="http://www.brisbanetimes.com.au/business/queenslands-cheapest-beachfront-address-********-ltfu.html" title="Queensland's cheapest beachfront address " style="">Queensland's cheapest beachfront address </a></li>
            <li><a href="http://www.brisbanetimes.com.au/business/queenslands-shonky-business-capital-revealed-********-lsqd.html" title="Queensland's shonky business capital revealed" style="">Queensland's shonky business capital revealed</a></li>
            <li><a href="http://www.brisbanetimes.com.au/business/westpac-rate-rise-pushes-customers-to-switch-banks-********-lsbd.html" title="Westpac rate rise 'pushes customers to switch banks'" style="">Westpac rate rise 'pushes customers to switch banks'</a></li>
            <li><a href="http://www.brisbanetimes.com.au/business/jetstar-and-airasia-in-lowcost-alliance-********-lsze.html" title="Jetstar and AirAsia in low-cost alliance" style="">Jetstar and AirAsia in low-cost alliance</a></li>
            <li><a href="http://www.brisbanetimes.com.au/business/red-hot-property-sector-points-to-rate-rise-********-ltb6.html" title="'Red hot' property sector points to rate rise" style="">'Red hot' property sector points to rate rise</a></li>
    </ol>

            </div>
            <div class="s2 lWAToday">
                <a href="http://www.watoday.com.au">Most viewed articles on WA Today</a>
                <h5>Top 5 <a href="http://www.watoday.com.au/business">Business</a> articles</h5>
    <ol>
            <li><a href="http://www.watoday.com.au/business/westpac-customers-walk-over-rate-rise-********-lscq.html" title="Westpac customers walk over rate rise" style="">Westpac customers walk over rate rise</a></li>
            <li><a href="http://www.watoday.com.au/business/a-strikes-twoyear-high-against-the-euro-********-ls79.html" title="$A strikes two-year high against the euro" style="">$A strikes two-year high against the euro</a></li>
            <li><a href="http://www.watoday.com.au/business/rip-out-vines-wine-industry-told-********-lsdj.html" title="Rip out vines, wine industry told" style="">Rip out vines, wine industry told</a></li>
            <li><a href="http://www.watoday.com.au/business/webjet-is-just-the-ticket-********-lsdu.html" title="Webjet is just the ticket" style="">Webjet is just the ticket</a></li>
            <li><a href="http://www.watoday.com.au/business/red-hot-property-sector-points-to-rate-rise-********-ltb6.html" title="'Red hot' property sector points to rate rise" style="">'Red hot' property sector points to rate rise</a></li>
    </ol>

            </div>
            <div class="s3 lAge">
                <a href="http://www.theage.com.au">Most viewed articles on The Age</a>
                <h5>Top 5 <a href="http://www.theage.com.au/business">Business</a> articles</h5>
    <ol>
            <li><a href="http://www.theage.com.au/business/westpac-customers-walk-over-rate-rise-********-lscq.html" title="Westpac customers walk over rate rise" style="">Westpac customers walk over rate rise</a></li>
            <li><a href="http://www.theage.com.au/business/a-strikes-twoyear-high-against-the-euro-********-ls79.html" title="$A strikes two-year high against the euro" style="">$A strikes two-year high against the euro</a></li>
            <li><a href="http://www.theage.com.au/business/rip-out-vines-wine-industry-told-********-lsdj.html" title="Rip out vines, wine industry told" style="">Rip out vines, wine industry told</a></li>
            <li><a href="http://www.theage.com.au/business/red-hot-property-sector-points-to-rate-rise-********-ltb6.html" title="'Red hot' property sector points to rate rise" style="">'Red hot' property sector points to rate rise</a></li>
            <li><a href="http://www.theage.com.au/business/markets/shares-flat-as-miners-offset-bank-falls-********-lsod.html" title="Shares flat as miners offset bank falls" style="">Shares flat as miners offset bank falls</a></li>
    </ol>

            </div>
            <div class="s4 lSmh">
                <a href="http://www.smh.com.au">Most viewed articles on The Sydney Morning Herald</a>
                <h5>Top 5 <a href="http://www.smh.com.au/business">Business</a> articles</h5>
    <ol>
            <li><a href="http://www.smh.com.au/business/westpac-rate-rise-pushes-customers-to-switch-banks-********-lsbd.html" title="Westpac rate rise 'pushes customers to switch banks'" style="">Westpac rate rise 'pushes customers to switch banks'</a></li>
            <li><a href="http://www.smh.com.au/business/a-strikes-twoyear-high-against-the-euro-********-ls79.html" title="$A strikes two-year high against the euro" style="">$A strikes two-year high against the euro</a></li>
            <li><a href="http://www.smh.com.au/business/red-hot-property-sector-points-to-rate-rise-********-ltb6.html" title="'Red hot' property sector points to rate rise" style="">'Red hot' property sector points to rate rise</a></li>
            <li><a href="http://www.smh.com.au/business/jetstar-and-airasia-in-lowcost-alliance-********-lsze.html" title="Jetstar and AirAsia in low-cost alliance" style="">Jetstar and AirAsia in low-cost alliance</a></li>
            <li><a href="http://www.smh.com.au/business/rip-out-vineyards-lehmann-********-ls76.html" title="Rip out vineyards: Lehmann" style="">Rip out vineyards: Lehmann</a></li>
    </ol>

            </div>
            <div class="s5">
                <h5>Videos</h5>
    <ol>
            <li><a href="http://media.smh.com.au/sport/sports-hq/rooney-key-for-man-united-1017709.html" title="Rooney key for Man United" style="">Rooney key for Man United</a></li>
            <li><a href="http://media.smh.com.au/entertainment/red-carpet/nude-hawkins-cover-defended-1012683.html" title="Nude Hawkins cover defended" style="">Nude Hawkins cover defended</a></li>
            <li><a href="http://media.smh.com.au/watson-chokes-on-a-ton-again-1015990.html" title="Watson chokes on a ton again" style="">Watson chokes on a ton again</a></li>
            <li><a href="http://media.smh.com.au/world/world-news/uk-endures-deep-freeze-1016948.html" title="UK endures deep freeze" style="">UK endures deep freeze</a></li>
            <li><a href="http://media.smh.com.au/national/national-news/1911-antarctic-plane-recovered-1017703.html" title="1911 Antarctic plane recovered" style="">1911 Antarctic plane recovered</a></li>
    </ol>

            </div>
</div>

    <div id="adspot-468x60-pos-2" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-468x60-pos-2",
            iframeId: "adspot-468x60-pos-2-iframe",
            params: $merge($merge(FD.baseAd.params, {
                    pos: 2,
                    adtype: 'panorama',
                aamsz : "468x60"
        }),getAdParams("468x60"))

            })
        );
    }
);

</script>
    <!-- Footer section links -->
    <ul class="fSectionLinks cfix">
            <li ><a href="http://www.smh.com.au/" title="SMH Home">SMH Home</a></li>
            <li ><a href="http://www.smh.com.au/news/national/" title="National">National</a></li>
            <li ><a href="http://www.smh.com.au/news/world/" title="World">World</a></li>
            <li ><a href="http://www.smh.com.au/opinion" title="Opinion">Opinion</a></li>
            <li ><a href="http://www.smh.com.au/business/" title="Business">Business</a></li>
            <li ><a href="http://www.smh.com.au/technology/" title="Technology">Technology</a></li>
            <li ><a href="http://www.smh.com.au/sport/" title="Sport">Sport</a></li>
            <li ><a href="http://www.smh.com.au/entertainment/" title="Entertainment">Entertainment</a></li>
            <li ><a href="http://www.smh.com.au/lifestyle/" title="Life & Style">Life & Style</a></li>
            <li ><a href="http://www.smh.com.au/travel/" title="Travel">Travel</a></li>
            <li class="last"><a href="http://weather.smh.com.au/local.jsp" title="Weather">Weather</a></li>
    </ul>

    <!-- Footer masthead links and copyright -->
<div class="fMastheadLinks cfix">
            <ul class="span-4 cfix">
        <li class="first"><h5>Sydney Morning Herald</h5></li>
            <li><a href="http://www.smh.com.au/siteguide/" title="Sitemap">Sitemap</a></li>
            <li><a href="http://www.smh.com.au/aboutsmh/index.html" title="About Us">About Us</a></li>
            <li><a href="http://www.smh.com.au/contacts/" title="Contact Us">Contact Us</a></li>
            <li><a href="http://www.fairfax.com.au/privacy" title="Privacy">Privacy</a></li>
            <li><a href="http://www.fairfax.com.au/conditions" title="Conditions">Conditions</a></li>
            <li><a href="http://www.fairfax.com.au/agreement" title="Member Agreement">Member Agreement</a></li>
            <li><a href="http://www.adcentre.com.au/fairfax-digital-network.aspx" title="Advertise with Us">Advertise with Us</a></li>
    </ul>

            <ul class="span-4 cfix">
        <li class="first"><h5>Products & Services</h5></li>
            <li><a href="https://membercentre.fairfax.com.au/NewsletterSubscription.aspx" title="Newsletters">Newsletters</a></li>
            <li><a href="http://www.smh.com.au/rssheadlines" title="RSS News Feeds">RSS News Feeds</a></li>
            <li><a href="http://www.smh.com.au/mobile/" title="Mobile">Mobile</a></li>
            <li><a href="http://www.smh.com.au/text/" title="Text">Text</a></li>
            <li><a href="http://subscriptions.fairfax.com.au/smhlanding/" title="Subscribe">Subscribe</a></li>
    </ul>

            <ul class="span-4 cfix">
        <li class="first"><h5>Classifieds</h5></li>
            <li><a href="http://www.stayz.com.au/" title="Accommodation">Accommodation</a></li>
            <li><a href="http://www.drive.com.au/" title="Cars">Cars</a></li>
            <li><a href="http://www.rsvp.com.au/" title="Dating">Dating</a></li>
            <li><a href="http://mycareer.com.au/" title="Jobs">Jobs</a></li>
            <li><a href="http://www.domain.com.au/" title="Real Estate">Real Estate</a></li>
    </ul>

            <ul class="span-4 cfix">
        <li class="first"><h5>Other Sites</h5></li>
            <li><a href="http://www.theage.com.au/" title="The Age">The Age</a></li>
            <li><a href="http://www.smh.com.au/" title="Sydney Morning Herald">Sydney Morning Herald</a></li>
            <li><a href="http://www.watoday.com.au/" title="WA Today">WA Today</a></li>
            <li><a href="http://www.brisbanetimes.com.au/" title="Brisbane Times">Brisbane Times</a></li>
            <li><a href="http://www.businessday.com.au/" title="Business Day">Business Day</a></li>
            <li><a href="http://www.moneymanager.com.au/" title="Money Manager">Money Manager</a></li>
            <li><a href="http://www.essentialbaby.com.au/" title="Essential Baby">Essential Baby</a></li>
            <li><a href="http://www.fairfax.com.au/map.ac" title="Fairfax Digital Network">Fairfax Digital Network</a></li>
    </ul>

    <a class="footer-logo" href="http://www.fairfaxdigital.com.au/">Fairfax Digital</a>
    <cite>Copyright  &#169;
        <script type="text/javascript">//<![CDATA[
            var today = new Date();
            document.write(today.getFullYear() + '.');
        //]]></script>
        Fairfax Digital</cite>

</div>
<!-- This comment fixes IE6's logo problem. Might be temporary until we've got content below this point -->
    <!-- START Nielsen Online 2008 survey launch -->
<script type="text/javascript">
var _rsCI="qt0303-fairfax"; var _rsND = "//secure-au.imrworldwide.com/";
document.write('<scr' + 'ipt type="text/javascript" src="' + _rsND + 'cgi-bin/j?ci=' + _rsCI + '&se=1&te=0&rd=' + (new Date()).getTime() + '"><\/scr' + 'ipt>');
</script>
<!-- END Nielsen Online -->
</div>
<!-- End fN-footerNetwork -->
    <script type="text/javascript">
            function isInternalReferrer() {
            var internalDomains = [
                "fairfaxdigital",
                "smh",
                "theage",
                "watoday",
                "brisbanetimes",
                "smh",
                "theage",
                "brisbanetimes",
                "watoday",
                "businessday",
                "tradingroom",
                "moneymanager",
                "investsmart",
                "afr",
                "afrboss",
                "mysmallbusiness",
                "newsbreak",
                "rubgyheaven",
                "realfooty",
                "leaguehq",
                "thevine",
                "fairfax",
                "sunherald",
                "cracker",
                "nationaltimes",
                "essentialbaby",
                "2ue",
                "3aw",
                "4bc",
                "4bh",
                "6pr",
                "96fm",
                "magic1278",
                "mydj",
                "rsvp",
                "domain",
                "homepriceguide",
                "mycareer",
                "thebigchair",
                "drive",
                "stayz",
                "businessday"
            ];

            var referrer = document.referrer;
            if (referrer == null || referrer == '') {
                return true;
            }

            var regex = new RegExp("//([a-z0-9_\\-\\.]+)[:0-9]*/"),
                match = regex.exec(referrer, "gi");
            if (match && match.length > 1) {
                for (var i in internalDomains) {
                    if (match[1].indexOf(internalDomains[i] + ".com.au") >= 0) {
                        return true;
                    }
                }
            }

            return false;
        }


    function getQueryVariable(variable) {
		var query = window.location.search.substring(1);
		var vars = query.split("&");
		for (var i=0;i<vars.length;i++) {
			var pair = vars[i].split("=");
			if (pair[0] == variable) {
				return pair[1];
			}
		}
		return '';
	}

    function google_ad_request_done(google_ads) {

        function buildGoogleAd(googleAd) {
            if (googleAd && googleAd.type == "text") {
                var adContent = [];
                adContent.push('<div style="margin-bottom:5px"><a href="');
                adContent.push(googleAd.url);
                adContent.push('" style="text-decoration:none">');
                adContent.push('<h4 style="margin:0 5px;color:#039;font-size:13px;text-decoration:underline;font-weight:normal">');
                adContent.push(googleAd.line1);
                adContent.push('</h4><p style="margin:0 5px;color:#000;font-size:12px">');
                adContent.push(googleAd.line2);
                adContent.push(googleAd.line3);
                adContent.push('</p><p style="margin:0 5px;color:#900">');
                adContent.push(googleAd.visible_url);
                adContent.push('</p></a></div>');

                return adContent.join("");
            }
        }

        /*
        * This function is required and is used to display
        * the ads that are returned from the JavaScript
        * request. You should modify the document.write
        * commands so that the HTML they write out fits
        * with your desired ad layout.
        */
        var s = '';
        var i;

        /*
        * Verify that there are actually ads to display.
        */
        if (google_ads.length == 0) {
            return;
        }

        /*
        * If an image or Flash ad is returned, display that ad.
        * Otherwise, build a string containing all of the ads and
        * then use a document.write() command to print that string.
        */
        var googleAds = $("googleAds"),
            moreGoogleAds = $("moreGoogleAds")
            split = 1,
            maxAdsInBlock = 3;

        if (googleAds) {
            var allAds = [];

            window.addEvent("domready", function() {
                // Populate the top adspot
                var adsByGoogle = "<h4 style='margin: 5px; font-size: 12px;'>Ads by Google</h4>",
                    googleAdsContent = [],
                    moreGoogleAdsContent = [adsByGoogle],
                    adIndex = 0,
                    adsInMore = 0;
                for (var i = 0, g = google_ads.length; i < g; i++) {
                    if (i < split) {
                            if (!isInternalReferrer()) {
                                if (i == 0) {
                                    googleAdsContent.push(adsByGoogle);
                                }
                                googleAdsContent.push(buildGoogleAd(google_ads[adIndex]));
                                adIndex++;
                            }
                    } else if (adsInMore < maxAdsInBlock) {
                        moreGoogleAdsContent.push(buildGoogleAd(google_ads[adIndex]));
                        adsInMore++;
                        adIndex++;
                    }
                }
                var googleAdsContentString = googleAdsContent.join("");
                if (googleAdsContentString != "") {
                    googleAds.set('html', googleAdsContentString);
                } else {
                    googleAds.destroy();
                }
                moreGoogleAds.set('html', moreGoogleAdsContent.join(""));
            });
        }
    }

    // Sets the hints to be the keyword for google ads
    var keyword = getQueryVariable('text');
    if (keyword != null && keyword != "") {
         google_hints = keyword.replace("+", ",");
    }

    google_ad_client = 'ca-fairfax-smh_js';
    google_ad_channel = 'Business';
    google_ad_output = 'js';
    google_max_num_ads = '4';
    google_ad_type = 'text';
    google_encoding = 'utf8';
    google_safe = 'high';
    google_ad_section = 'default';
</script>

<!--
  /*
   * The JavaScript returned from the following page uses
   * the parameter values assigned above to populate an array
   * of ad objects. Once that array has been populated,
   * the JavaScript will call the google_ad_request_done
   * function to display the ads.
   */
-->
<script type="text/javascript" src="http://pagead2.googlesyndication.com/pagead/show_ads.js"></script>
    <!--START Nielsen//NetRatings SiteCensus V5.2 -->
<!--COPYRIGHT 2006 Nielsen//NetRatings -->
<script type="text/javascript">
	var _rsCI="f2";
	var _rsCG="SMH-business-column-michaelpascoe-story-online";
	var _rsDN="//secure-au.imrworldwide.com/";
	var _rsCC=0;
</script>
<script type="text/javascript" src="http://secure-au.imrworldwide.com/v53.js">
</script>
<noscript>
	<img src="http://secure-au.imrworldwide.com/cgi-bin/m?ci=f2&amp;cg=SMH-business-column-michaelpascoe-story-online" alt="" />
</noscript>
<!--END Nielsen//NetRatings SiteCensus V5.2 -->

<!-- SiteCatalyst code version: H.20.2.
Copyright 1997-2009 Omniture, Inc. More info available at
http://www.omniture.com -->
<script language="JavaScript" type="text/javascript" src="http://resources.smh.com.au/smh/media-common-1.0/js/s_code.js"></script>
<script language="JavaScript" type="text/javascript"><!--
/* You may give each page an identifying name, server, and channel on the next lines. */
s.pageName="smh:article:business:column:michael pascoe:the board’s next fear the female quota"
s.server=""
s.channel="business"
s.pageType=""
s.prop3="smh:business:column:michael pascoe"
s.prop5="1017890"
s.prop13="michael pascoe"
s.prop2="online"
s.prop4="dcds:common templates"
s.prop1="business:the board’s next fear the female quota"
/* Conversion Variables */
s.campaign=""
s.state=""
s.zip=""
s.events="event1"
s.products=""
s.purchaseID=""
s.hier1="business|column|michael pascoe|the board’s next fear: the female quota"
/************* DO NOT ALTER ANYTHING BELOW THIS LINE ! **************/
var s_code=s.t();if(s_code)document.write(s_code)//--></script>
<script language="JavaScript" type="text/javascript"><!--
if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-')
//--></script><noscript><img
src="http://f2nsmh.112.2O7.net/b/ss/f2nsmh/1/H.20.2--NS/0?[AQB]&cdp=3&[AQE]"
height="1" width="1" border="0" alt="" /></noscript><!--/DO NOT REMOVE/-->
<!-- End SiteCatalyst code version: H.20.2. -->

    <script type="text/javascript">if(window['FD']){function initPingServer(){var v = new Date().getTime();new Element("script",{src:"/action/pingServerAction?par=1017890&v=" + v}).inject($$("head")[0]);}FD.register("PingServer");}</script>

    <div id="adspot-1x1" class="ad"></div>

<script type="text/javascript">
        if (!autoStartEnabled() && isInternalReferrer()) {

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-1x1",
            iframeId: "adspot-1x1-iframe",
            params: $merge($merge(FD.baseAd.params, {
                aamsz : "1x1"
        }),getAdParams("1x1"))

            })
        );
    }
);
            }

</script>
    <div id="adspot-1x11" class="ad"></div>

<script type="text/javascript">

    delayedAds.push(function(){
        FD.addAd($merge(FD.baseAd, {
            id: "adspot-1x11",
            iframeId: "adspot-1x11-iframe",
            params: $merge($merge(FD.baseAd.params, {
                    adtype: 'panorama',
                aamsz : "1x11"
        }),getAdParams("1x11"))

            })
        );
    }
);

</script>
</div>

</body>
</html>