<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." default="build" name="scm-example">
    <property environment="env"/>
    <property name="junit.output.dir" value="ant-target/reports"/>
    <property name="debuglevel" value="source,lines,vars"/>
    <property name="target" value="1.8"/>
    <property name="source" value="1.8"/>
    <path id="tmurali2.classpath">
        <pathelement location="target/test-classes"/>
        <pathelement location="target/classes"/>
        <pathelement location="ant-libs/gson-2.7.jar"/>
        <pathelement location="ant-libs/hamcrest-core-1.3.jar"/>
        <pathelement location="ant-libs/javax.servlet-api-3.1.0.jar"/>
        <pathelement location="ant-libs/jetty-http-9.2.28.v20190418.jar"/>
        <pathelement location="ant-libs/jetty-io-9.2.28.v20190418.jar"/>
        <pathelement location="ant-libs/jetty-security-9.2.28.v20190418.jar"/>
        <pathelement location="ant-libs/jetty-server-9.2.28.v20190418.jar"/>
        <pathelement location="ant-libs/jetty-servlet-9.2.28.v20190418.jar"/>
        <pathelement location="ant-libs/jetty-util-9.2.28.v20190418.jar"/>
        <pathelement location="ant-libs/junit-4.12.jar"/>
    </path>
    <!--
    # Task for cleaning all files generated from last Ant build 
    -->
    <target name="clean">
        <delete dir="ant-target/test-classes"/>
        <delete dir="ant-target/classes"/>
    </target>
    <!--
    # Compile all the Java source files from source to target directories 
    -->
    <target name="build">
        <echo message="${ant.project.name}: ${ant.file}"/>
        <mkdir dir="ant-target/classes"/>
        <javac debug="true" debuglevel="${debuglevel}" destdir="ant-target/classes" includeantruntime="false" source="${source}" target="${target}" encoding="UTF-8">
            <src path="src/main/java"/>
            <include name="**/*.java"/>
            <classpath refid="tmurali2.classpath"/>
        </javac>
    </target>
    <!--                                                                                                                   
    # Compile all the Java test files from source to target directories                                                  
    -->
    <target name="build-test" depends="build">
        <mkdir dir="ant-target/test-classes"/>
        <javac debug="true" debuglevel="${debuglevel}" destdir="ant-target/test-classes" includeantruntime="false" source="${source}" target="${target}" encoding="UTF-8">
            <src path="src/test/java"/>
            <include name="**/*.java"/>
            <classpath refid="tmurali2.classpath"/>
        </javac>

        <copy todir="ant-target/test-classes">
            <fileset dir="src/test/resources"/>
        </copy>
    </target>
    
    <!--                                                                                                                   
    # Run all the tests and store the junit report in the ${junit.output.dir}/html directory           
    -->
    <target name="test" depends="build-test">
        <mkdir dir="${junit.output.dir}"/>
        <mkdir dir="${junit.output.dir}/html"/>
        <junit fork="yes" printsummary="withOutAndErr">
            <formatter type="xml"/>
            <batchtest todir="${junit.output.dir}">
                <fileset dir="src/test/java">
                    <include name="**/*Test.java"/>
                </fileset>
            </batchtest>
            <classpath refid="tmurali2.classpath"/>
        </junit>
        <junitreport>
            <fileset dir="${junit.output.dir}">
                <include name="TEST-*.xml"/>  
            </fileset>
            <report format="frames" todir="${junit.output.dir}/html"/>
	</junitreport>
	<echo message="Please find the final JUnit test report at: ${junit.output.dir}/html/index.html"/>
    </target>
</project>