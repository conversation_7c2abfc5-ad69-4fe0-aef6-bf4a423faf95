/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/DataNode.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/internal/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/ParseErrorList.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/TokenQueue.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/helper/Validate.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/StructuralEvaluator.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/safety/Whitelist.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/Elements.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/Evaluator.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/ParseError.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Node.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/LeafNode.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Attribute.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/PseudoTextElement.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/NodeTraversor.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/NodeVisitor.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/FormElement.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/internal/Normalizer.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/HtmlTreeBuilder.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/Parser.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/examples/HtmlToPlainText.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/BooleanAttribute.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/internal/ConstrainableInputStream.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/Tag.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/Token.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/examples/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/NodeFilter.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Attributes.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/XmlDeclaration.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/ParseSettings.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/CDataNode.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/HtmlTreeBuilderState.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/CombiningEvaluator.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/examples/Wikipedia.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/TreeBuilder.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/Selector.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Document.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/XmlTreeBuilder.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/safety/Cleaner.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/internal/StringUtil.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/examples/ListLinks.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/helper/HttpConnection.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/safety/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/helper/DataUtil.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/EntitiesData.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Comment.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/CharacterReader.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/TextNode.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/UnsupportedMimeTypeException.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/UncheckedIOException.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Entities.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/Element.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/HttpStatusException.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/Connection.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/Jsoup.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/helper/ChangeNotifyingArrayList.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/DocumentType.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/Tokeniser.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/Collector.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/select/QueryParser.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/nodes/NodeUtils.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/package-info.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/parser/TokeniserState.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/SerializationException.java
/Users/<USER>/Desktop/jsoup/src/main/java/org/jsoup/helper/W3CDom.java
