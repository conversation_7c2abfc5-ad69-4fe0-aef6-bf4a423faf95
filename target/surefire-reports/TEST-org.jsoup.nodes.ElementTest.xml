<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.jsoup.nodes.ElementTest" time="0.049" tests="99" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar /Users/<USER>/Desktop/jsoup/target/surefire 2025-09-13T23-44-41_922-jvmRun1 surefire-20250913234442069_1tmp surefire_0-20250913234442069_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="justin"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.6.1"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="testFormatOutline" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="parentlessToString" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="insertChildrenArgumentValidation" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testPrependElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testOuterHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHashcodeIsStableWithContentChanges" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="voidTestFilterCallReturnsElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="elementByTagName" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClonesClassnames" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="appendMustCorrectlyMoveChildrenInsideOneParentElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="before" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemoveBeforeIndex" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testChainedRemoveAttributes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="booleanAttributeOutput" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddNewHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddNewText" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testPreviousElementSiblings" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementById" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClone" classname="org.jsoup.nodes.ElementTest" time="0.002"/>
  <testcase name="testHtmlContainsOuter" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClassDomMethods" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testRoot" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testWrap" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testNamespacedElements" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testClassNames" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testElementSiblingIndex" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testIs" classname="org.jsoup.nodes.ElementTest" time="0.003"/>
  <testcase name="testEmptyElementFormatHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testLoopedRemoveAttributes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testShadowElementsAreUpdated" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testKeepsPreTextAtDepth" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithAttribute" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNoIndentOnScriptAndStyle" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAppendRowToTable" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithClass" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testShallowClone" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testWrapWithRemainder" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClearAttributes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testKeepsPreTextInCode" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHasClassDomMethods" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetParents" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testGetText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="getElementsByTagName" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testFormatHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithAttributeValue" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testWholeText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetElementsWithAttributeDash" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testManipulateTextNodes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testChildThrowsIndexOutOfBoundsOnMissing" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testPrependRowToTable" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetTextNodes" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="after" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testContainerOutput" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testInnerHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testHasText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemoveAttr" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="textHasSpaceBetweenDivAndCenterTags" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetDataNodes" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testCssPath" classname="org.jsoup.nodes.ElementTest" time="0.008"/>
  <testcase name="testSetHtmlTitle" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testChildrenElements" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="moveByAppend" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNormalisesText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testClassUpdates" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddNewElement" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testHashAndEqualsAndValue" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testElementSiblingIndexSameContent" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetChildText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRelativeUrls" classname="org.jsoup.nodes.ElementTest" time="0.003"/>
  <testcase name="testSetIndent" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testThrowsOnAddNullText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetSiblingsWithDuplicateContent" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testNormalizesInvisiblesInText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="insertChildrenAsCopy" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testTagNameSet" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="dataset" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testPrependText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNormalizesNbspInText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemoveAfterIndex" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testThrowsOnPrependNullText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testGetSiblings" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testSetHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testSetText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNextElementSiblings" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testAddBooleanAttribute" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="textHasSpaceAfterBlockTags" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="whiteSpaceClassElement" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="classNamesAndAttributeNameIsCaseInsensitive" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testPrependNewHtml" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testRemovingEmptyClassAttributeWhenLastClassRemoved" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testBrHasSpace" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testKeepsPreText" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNotPretty" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="testNextElementSiblingAfterClone" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testAppendTo" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="getNamespacedElementsByTag" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="testTraverse" classname="org.jsoup.nodes.ElementTest" time="0.001"/>
  <testcase name="insertChildrenAtPosition" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
  <testcase name="elementIsNotASiblingOfItself" classname="org.jsoup.nodes.ElementTest" time="0.0"/>
</testsuite>