<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.jsoup.parser.HtmlParserTest" time="0.171" tests="132" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar /Users/<USER>/Desktop/jsoup/target/surefire 2025-09-13T23-44-41_922-jvmRun1 surefire-20250913234442069_1tmp surefire_0-20250913234442069_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="justin"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.6.1"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="preservedCaseLinksCantNest" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisesHeadlessBody" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesUnterminatedOption" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesProtocolRelativeUrl" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="dropsUnterminatedTag" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="canPreserveAttributeCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesNullInComments" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesTextAfterData" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="dropsUnterminatedAttribute" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testNormalisesIsIndex" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="noTableDirectInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testSupportsPartiallyNonAsciiTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSpaceAfterTag" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesXmlDeclarationAsBogusComment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisesDocument" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="createsStructureFromBodySnippet" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="retainsAttributesOfDifferentCaseIfSensitive" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="canPreserveTagCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="preservesSpaceInScript" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesXmlDeclAndCommentsBeforeDoctype" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="testHtmlLowerCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesDeepStack" classname="org.jsoup.parser.HtmlParserTest" time="0.119"/>
  <testcase name="parsesSimpleDocument" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="doesNotCreateImplicitLists" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesUnterminatedTextarea" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSupportsNonAsciiTags" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="dropsDuplicateAttributes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesWhatWgExpensesTableExample" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesImplicitCaptionClose" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesQuotesInCommentsInScripts" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesInputInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesInvalidStartTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesEscapedData" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="fallbackToUtfIfCantEncode" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="reconstructFormattingElements" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="createsDocumentStructure" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCdataInScript" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedTitle" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="caseInsensitiveParseTree" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handleNullContextInParseFragment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesUnterminatedComments" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="emptyTdTag" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="caseSensitiveParseTree" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testFragment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedScriptAtEof" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesNewlinesAndWhitespaceInTag" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesDataOnlyTags" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="normalizesDiscordantTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCdataAcrossBuffer" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="noImplicitFormForTextAreas" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="commentBeforeHtml" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedCdataAtEOF" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCdata" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesKnownEmptyBlocks" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="createsFormElements" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="noErrorsByDefault" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testHeaderContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testRelaxedTags" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesKnownEmptyStyle" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyTitle" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="preservesSpaceInTextArea" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnexpectedMarkupInTables" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyIframe" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="characterReaderBuffer" classname="org.jsoup.parser.HtmlParserTest" time="0.002"/>
  <testcase name="testTemplateInsideTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="cdataNodesAreTextNodes" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="ignoresContentAfterFrameset" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesBaseTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSpanContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handles0CharacterAsText" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesFrames" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesJavadocFont" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testReinsertionModeForThCelss" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesControlCodeInAttributeName" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesInvalidDoctypes" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesTagsInTextarea" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesCommentsInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesEscapedScript" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testInvalidTableContents" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesUnknownTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="ignoresDupeEndTrTag" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="associatedFormControlsWithDisjointForms" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesTbodyTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisedBodyAfterContent" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesSpanInTbody" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesUnclosedTitleAtEof" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesSolidusAtAttributeEnd" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnknownNamespaceTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesKnownEmptyNoFrames" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="roundTripsCdata" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesManyChildren" classname="org.jsoup.parser.HtmlParserTest" time="0.018"/>
  <testcase name="handlesTextArea" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="convertsImageToImg" classname="org.jsoup.parser.HtmlParserTest" time="0.002"/>
  <testcase name="preSkipsFirstNewline" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="commentAtEnd" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="selfClosingOnNonvoidIsError" classname="org.jsoup.parser.HtmlParserTest" time="0.002"/>
  <testcase name="handlesNestedImplicitTable" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesRoughAttributes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testUsingSingleQuotesInQueries" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesUnclosedDefinitionLists" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesWhitespaceInoDocType" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="reconstructFormattingElementsInTable" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="selfClosingVoidIsNotAnError" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testNoImagesInNoScriptInHead" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesBaseWithoutHref" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnclosedRawtextAtEof" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="tracksLimitedErrorsWhenRequested" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="doesNotFindShortestMatchingEntity" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="tracksErrorsWhenRequested" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesUnclosedAnchors" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="parsesBodyFragment" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesUnknownInlineTags" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testSelectWithOption" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="normalisesEmptyDocument" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="relaxedBaseEntityMatchAndStrictExtendedMatch" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesUnclosedFormattingElements" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesSolidusInA" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testHgroup" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="discardsNakedTds" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesComments" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testFontFlowContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesMisnestedTagsBI" classname="org.jsoup.parser.HtmlParserTest" time="0.001"/>
  <testcase name="handlesMisnestedTagsBP" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="canPreserveBothCase" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testHandlesDeepSpans" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handleCDataInText" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="findsCharsetInMalformedMeta" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesMultiClosingBody" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesBlocksInDefinitions" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="testAFlowContents" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="handlesNullInData" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
  <testcase name="parsesQuiteRoughAttributes" classname="org.jsoup.parser.HtmlParserTest" time="0.0"/>
</testsuite>