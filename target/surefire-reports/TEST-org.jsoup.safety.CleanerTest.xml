<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.jsoup.safety.CleanerTest" time="0.008" tests="34" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar /Users/<USER>/Desktop/jsoup/target/surefire 2025-09-13T23-44-41_922-jvmRun1 surefire-20250913234442069_1tmp surefire_0-20250913234442069_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="justin"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.6.1"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="testHandlesEmptyAttributes" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="basicBehaviourTest" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="handlesCustomProtocols" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testIsValidBodyHtml" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="handlesNoHrefAttribute" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="preservesRelativeLinksIfConfigured" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testCleanAnchorProtocol" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="bailsIfRemovingProtocolThatsNotSet" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testDropScript" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testRemoveTags" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testRemoveAttributes" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testRemoveEnforcedAttributes" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testRelaxed" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="resolvesRelativeLinks" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testDropComments" classname="org.jsoup.safety.CleanerTest" time="0.001"/>
  <testcase name="handlesFramesets" classname="org.jsoup.safety.CleanerTest" time="0.001"/>
  <testcase name="handlesControlCharactersAfterTagName" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="simpleBehaviourTest2" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testRemoveProtocols" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testDropImageScript" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="simpleBehaviourTest" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="handlesAttributesWithNoValue" classname="org.jsoup.safety.CleanerTest" time="0.001"/>
  <testcase name="dropsUnresolvableRelativeLinks" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testScriptTagInWhiteList" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="addsTagOnAttributesIfNotSet" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testDropsUnknownTags" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testCleanJavascriptHref" classname="org.jsoup.safety.CleanerTest" time="0.001"/>
  <testcase name="basicWithImagesTest" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="whitelistedProtocolShouldBeRetained" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testDropXmlProc" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="testIsValidDocument" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="handlesAllPseudoTag" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
  <testcase name="supplyOutputSettings" classname="org.jsoup.safety.CleanerTest" time="0.001"/>
  <testcase name="cleansInternationalText" classname="org.jsoup.safety.CleanerTest" time="0.0"/>
</testsuite>