<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.jsoup.select.SelectorTest" time="0.025" tests="62" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar /Users/<USER>/Desktop/jsoup/target/surefire 2025-09-13T23-44-41_922-jvmRun1 surefire-20250913234442069_1tmp surefire_0-20250913234442069_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Desktop/jsoup/target/test-classes:/Users/<USER>/Desktop/jsoup/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.7/gson-2.7.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.2.28.v20190418/jetty-server-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.2.28.v20190418/jetty-http-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.2.28.v20190418/jetty-util-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.2.28.v20190418/jetty-io-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.2.28.v20190418/jetty-servlet-9.2.28.v20190418.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.2.28.v20190418/jetty-security-9.2.28.v20190418.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Desktop/jsoup/target/surefire/surefirebooter-20250913234442069_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="justin"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.6.1"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/nz/21ng7vp976v7y8h3s8r108f40000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/Desktop/jsoup"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="containsData" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testByAttributeStarting" classname="org.jsoup.select.SelectorTest" time="0.002"/>
  <testcase name="deeperDescendant" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="selectSameElements" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testPseudoEquals" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testByClassCaseInsensitive" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testByAttributeRegexCharacterClass" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testAllWithClass" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testByTag" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testById" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="findBetweenSpan" classname="org.jsoup.select.SelectorTest" time="0.002"/>
  <testcase name="descendant" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="handlesCommasInSelector" classname="org.jsoup.select.SelectorTest" time="0.002"/>
  <testcase name="notAll" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testByAttribute" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="selectClassWithSpace" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="notAdjacent" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testCombinedWithContains" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="containsWithQuote" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testPseudoGreaterThan" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testAllElements" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testByAttributeRegex" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="mixCombinator" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testRelaxedTags" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testPseudoBetween" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testPseudoLessThan" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testGroupOr" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testByClass" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="and" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="attributeWithBrackets" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testPsuedoContainsWithParentheses" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testGroupOrAttribute" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="caseInsensitive" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="selectFirst" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="containsOwn" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testCharactersInIdAndClass" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="splitOnBr" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="multiChildDescent" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="adjacentSiblingsWithId" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testMatches" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="matchText" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="selectFirstWithOr" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="matchTextAttributes" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="parentWithClassChild" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="generalSiblings" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="parentChildStar" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testPseudoCombined" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testPseudoContains" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testPseudoHas" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="notClass" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="notParas" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="selectSupplementaryCharacter" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="adjacentSiblings" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testNamespacedTag" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testNestedHas" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="parentChildElement" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="testByAttributeRegexCombined" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="matchesOwn" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testSupportsLeadingCombinator" classname="org.jsoup.select.SelectorTest" time="0.0"/>
  <testcase name="mixCombinatorGroup" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="testWildcardNamespacedTag" classname="org.jsoup.select.SelectorTest" time="0.001"/>
  <testcase name="selectFirstWithAnd" classname="org.jsoup.select.SelectorTest" time="0.0"/>
</testsuite>